'use client';

import { motion } from 'framer-motion';
import { StoryContextProps } from '@/types';

const StoryContext: React.FC<StoryContextProps> = ({ story, signature, isVisible }) => {
  const words = story.split(' ');

  return (
    <motion.div
      className="relative bg-vintage-sepia/30 backdrop-blur-sm rounded-2xl p-8 border border-vintage-gold/20 shadow-lg max-w-2xl mx-auto"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Story text with typewriter effect */}
      <div className="font-kalam text-lg text-vintage-brown leading-relaxed mb-6 text-center">
        {words.map((word, index) => (
          <motion.span
            key={index}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{
              duration: 0.1,
              delay: index * 0.05
            }}
            className="inline-block mr-1"
          >
            {word}
          </motion.span>
        ))}
      </div>

      {/* Brother's signature */}
      <motion.div
        className="text-center"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.5,
          delay: words.length * 0.05 + 0.5
        }}
      >
        <p className="font-kalam text-vintage-brown/80 italic text-lg">
          {signature}
        </p>
      </motion.div>

      {/* Decorative elements */}
      <div className="absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-vintage-gold/30 rounded-tl-lg" />
      <div className="absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-vintage-gold/30 rounded-tr-lg" />
      <div className="absolute bottom-4 left-4 w-8 h-8 border-l-2 border-b-2 border-vintage-gold/30 rounded-bl-lg" />
      <div className="absolute bottom-4 right-4 w-8 h-8 border-r-2 border-b-2 border-vintage-gold/30 rounded-br-lg" />
    </motion.div>
  );
};

export default StoryContext;
