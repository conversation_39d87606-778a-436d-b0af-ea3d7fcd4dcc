'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { PhotoStageProps } from '@/types';
import StoryContext from './StoryContext';
import InputComponent from './InputComponent';
import VideoPlayer from './VideoPlayer';
import LoadingAnimation from './LoadingAnimation';
import UnlockAnimation from './UnlockAnimation';
import { useVideoGeneration } from '@/hooks/useVideoGeneration';
import { useAppStore } from '@/stores/appStore';

const PhotoStage: React.FC<PhotoStageProps> = ({ photo, isActive, onPromptSubmit }) => {
  const [showStory, setShowStory] = useState(false);
  const [showInput, setShowInput] = useState(false);
  const [generatedVideoUrl, setGeneratedVideoUrl] = useState<string | null>(null);
  const [showVideo, setShowVideo] = useState(false);
  const [showUnlockAnimation, setShowUnlockAnimation] = useState(false);

  const { generateVideo, isGenerating } = useVideoGeneration();
  const { currentPhotoIndex } = useAppStore();

  const handlePhotoClick = () => {
    if (!showStory) {
      setShowStory(true);
    } else if (!showInput) {
      setShowInput(true);
    }
  };

  const handlePromptSubmit = async (prompt: string) => {
    setShowInput(false);

    // Generate video
    const videoUrl = await generateVideo(photo.id, prompt);

    if (videoUrl) {
      setGeneratedVideoUrl(videoUrl);
      setShowVideo(true);
    }

    onPromptSubmit(prompt);
  };

  const handleVideoComplete = () => {
    setShowVideo(false);
    setShowStory(false);
    setGeneratedVideoUrl(null);

    // Show unlock animation for next photo
    setShowUnlockAnimation(true);
  };

  const handleUnlockComplete = () => {
    setShowUnlockAnimation(false);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Main Photo Display */}
      <div className="flex flex-col items-center space-y-8">
        <motion.div
          className="relative cursor-pointer group w-full max-w-2xl"
          onClick={handlePhotoClick}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          layout
        >
          <div className="relative w-full vintage-frame rounded-lg overflow-hidden">
            <Image
              src={photo.src}
              alt={photo.alt}
              width={800}
              height={600}
              className="w-full h-auto object-cover"
              priority
            />

            {/* Film grain overlay */}
            <div className="film-grain absolute inset-0" />

            {/* Photo metadata overlay */}
            <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-2 rounded-lg backdrop-blur-sm">
              <p className="font-kalam text-sm">
                {photo.date}
              </p>
              {photo.location && (
                <p className="font-inter text-xs opacity-80">
                  {photo.location}
                </p>
              )}
            </div>

            {/* Interaction hint */}
            {!showStory && (
              <motion.div
                className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                initial={{ opacity: 0 }}
                animate={{ opacity: 0 }}
                whileHover={{ opacity: 1 }}
              >
                <div className="bg-vintage-gold text-vintage-brown px-6 py-3 rounded-full font-poppins font-medium">
                  Click to begin the story
                </div>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Story Context */}
        <AnimatePresence>
          {showStory && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="w-full max-w-2xl"
            >
              <StoryContext
                story={photo.contextStory}
                signature={photo.brotherSignature}
                isVisible={showStory}
              />

              {/* Continue button */}
              {!showInput && (
                <motion.div
                  className="text-center mt-8"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1 }}
                >
                  <button
                    onClick={() => setShowInput(true)}
                    className="bg-vintage-gold hover:bg-vintage-gold/90 text-vintage-brown px-8 py-3 rounded-full font-poppins font-medium transition-colors duration-200 shadow-lg hover:shadow-xl"
                  >
                    What happened next?
                  </button>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Input Component */}
        <AnimatePresence>
          {showInput && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="w-full max-w-2xl"
            >
              <InputComponent
                onSubmit={handlePromptSubmit}
                isLoading={isGenerating}
                placeholder="Imagine what happened in the next 10 seconds..."
                maxLength={300}
                minLength={20}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Video Player */}
        <AnimatePresence>
          {showVideo && generatedVideoUrl && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="w-full max-w-2xl"
            >
              <VideoPlayer
                videoUrl={generatedVideoUrl}
                isVisible={showVideo}
                onComplete={handleVideoComplete}
              />
            </motion.div>
          )}
        </AnimatePresence>

      {/* Loading Animation */}
      <LoadingAnimation
        loadingState={{
          type: 'video-generation',
          message: 'Creating your magical moment...'
        }}
        isVisible={isGenerating}
      />

      {/* Unlock Animation */}
      <UnlockAnimation
        isVisible={showUnlockAnimation}
        onComplete={handleUnlockComplete}
        photoIndex={currentPhotoIndex}
      />
    </div>
  );
};

export default PhotoStage;
