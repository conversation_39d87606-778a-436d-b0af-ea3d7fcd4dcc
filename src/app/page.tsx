'use client';

import { useAppStore } from '@/stores/appStore';
import Timeline from '@/components/Timeline';
import PhotoStage from '@/components/PhotoStage';
import ProgressIndicator from '@/components/ProgressIndicator';
import { motion } from 'framer-motion';

export default function Home() {
  const { photos, currentPhotoIndex, setCurrentPhoto } = useAppStore();
  const currentPhoto = photos[currentPhotoIndex];

  const handlePhotoSelect = (index: number) => {
    setCurrentPhoto(index);
  };

  const handlePromptSubmit = async (prompt: string) => {
    // TODO: Implement video generation logic
    console.log('Prompt submitted:', prompt);
  };

  return (
    <div className="min-h-screen bg-vintage-off-white relative">
      {/* Film grain overlay */}
      <div className="film-grain fixed inset-0 pointer-events-none z-10" />

      {/* Progress Indicator */}
      <ProgressIndicator />

      {/* Main content container */}
      <div className="relative z-20 min-h-screen flex flex-col">
        {/* Header */}
        <motion.header
          className="text-center py-8 md:py-12 px-4"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="font-playfair text-3xl sm:text-4xl md:text-6xl text-vintage-brown mb-4">
            Moments Forward
          </h1>
          <p className="font-inter text-base md:text-lg text-vintage-brown/80 max-w-2xl mx-auto">
            A journey through our shared memories, where each photo unlocks the next chapter of our story
          </p>
        </motion.header>

        {/* Main content area */}
        <div className="flex-1 flex flex-col justify-center px-4 max-w-6xl mx-auto w-full">
          {/* Photo Stage */}
          <motion.div
            className="mb-12 md:mb-16"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <PhotoStage
              photo={currentPhoto}
              isActive={true}
              onPromptSubmit={handlePromptSubmit}
            />
          </motion.div>
        </div>

        {/* Timeline - Fixed at bottom */}
        <motion.div
          className="mt-auto py-8 px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div className="max-w-6xl mx-auto">
            <Timeline
              photos={photos}
              currentIndex={currentPhotoIndex}
              onPhotoSelect={handlePhotoSelect}
            />
          </div>
        </motion.div>
      </div>
    </div>
  );
}
