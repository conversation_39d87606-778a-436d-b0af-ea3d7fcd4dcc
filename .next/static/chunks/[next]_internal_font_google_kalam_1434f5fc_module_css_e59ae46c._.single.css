/* [next]/internal/font/google/kalam_1434f5fc.module.css [app-client] (css) */
@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/24456447ebf823a2-s.c7b8ef10.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/5cc7db20afa92dbd-s.2ca4b275.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/3b63e461c9a5c212-s.p.bd2122e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/e7f298fbbd85b231-s.d9bd01a9.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/c67e6d7dbcd84d7c-s.63b7d702.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/805d2b6f402542dd-s.p.439732b4.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/97ed52776c95a56f-s.a75c3b69.woff2") format("woff2");
  unicode-range: U+900-97F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}

@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/798f94188a5775f8-s.4def6ccc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Kalam;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/31bb1162bf9469dd-s.p.1944bce7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Kalam Fallback;
  src: local(Arial);
  ascent-override: 108.94%;
  descent-override: 54.42%;
  line-gap-override: 0.0%;
  size-adjust: 97.58%;
}

.kalam_1434f5fc-module__SIlQYa__className {
  font-family: Kalam, Kalam Fallback;
  font-style: normal;
}

.kalam_1434f5fc-module__SIlQYa__variable {
  --font-kalam: "Kalam", "Kalam Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_kalam_1434f5fc_module_css_e59ae46c._.single.css.map*/