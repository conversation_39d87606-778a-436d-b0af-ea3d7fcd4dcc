{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/stores/appStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\nimport { AppState, Photo, VideoGeneration } from '@/types';\n\ninterface AppStore extends AppState {\n  // Actions\n  setCurrentPhoto: (index: number) => void;\n  unlockNextPhoto: () => void;\n  addVideoGeneration: (generation: VideoGeneration) => void;\n  updateVideoGeneration: (id: string, updates: Partial<VideoGeneration>) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | undefined) => void;\n  initializePhotos: (photos: Photo[]) => void;\n  resetProgress: () => void;\n  getVideoForPhoto: (photoId: string) => VideoGeneration | undefined;\n  getCompletedVideos: () => VideoGeneration[];\n  getTotalProgress: () => number;\n}\n\n// Sample data - in a real app, this would come from an API\nconst samplePhotos: Photo[] = [\n  {\n    id: '1',\n    src: '/photos/photo1.svg',\n    alt: 'First childhood memory',\n    date: 'Summer 2010',\n    location: 'Grandma\\'s backyard',\n    isUnlocked: true,\n    contextStory: 'Remember this day? We were playing in <PERSON>\\'s garden, and you had just discovered that butterfly. You were so excited, jumping up and down, trying to catch it with your tiny hands.',\n    brotherSignature: '- Your loving brother ❤️'\n  },\n  {\n    id: '2',\n    src: '/photos/photo2.svg',\n    alt: 'Second childhood memory',\n    date: 'Winter 2011',\n    location: 'Living room',\n    isUnlocked: false,\n    contextStory: 'This was Christmas morning. You had just opened your favorite present - that stuffed elephant you carried everywhere for years. The look of pure joy on your face was priceless.',\n    brotherSignature: '- Your loving brother ❤️'\n  },\n  {\n    id: '3',\n    src: '/photos/photo3.svg',\n    alt: 'Third childhood memory',\n    date: 'Spring 2012',\n    location: 'Local park',\n    isUnlocked: false,\n    contextStory: 'Our first bike ride together! You were so determined to keep up with me, even though your legs could barely reach the pedals. You never gave up.',\n    brotherSignature: '- Your loving brother ❤️'\n  },\n  {\n    id: '4',\n    src: '/photos/photo4.svg',\n    alt: 'Fourth childhood memory',\n    date: 'Summer 2013',\n    location: 'Beach vacation',\n    isUnlocked: false,\n    contextStory: 'Building sandcastles at the beach. You insisted on making the tallest tower, and when the wave knocked it down, you just laughed and started building again.',\n    brotherSignature: '- Your loving brother ❤️'\n  },\n  {\n    id: '5',\n    src: '/photos/photo5.svg',\n    alt: 'Fifth childhood memory',\n    date: 'Fall 2014',\n    location: 'School playground',\n    isUnlocked: false,\n    contextStory: 'Your first day of school. You were nervous but trying to be brave. I promised I\\'d pick you up, and you made me pinky promise. You still remember that, don\\'t you?',\n    brotherSignature: '- Your loving brother ❤️'\n  }\n];\n\nexport const useAppStore = create<AppStore>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      photos: samplePhotos,\n      currentPhotoIndex: 0,\n      videoGenerations: [],\n      isLoading: false,\n      error: undefined,\n\n  // Actions\n  setCurrentPhoto: (index: number) => {\n    const { photos } = get();\n    if (index >= 0 && index < photos.length && photos[index].isUnlocked) {\n      set({ currentPhotoIndex: index });\n    }\n  },\n\n  unlockNextPhoto: () => {\n    set((state) => {\n      const nextIndex = state.currentPhotoIndex + 1;\n      if (nextIndex < state.photos.length) {\n        const updatedPhotos = state.photos.map((photo, index) => \n          index === nextIndex ? { ...photo, isUnlocked: true } : photo\n        );\n        return {\n          photos: updatedPhotos,\n          currentPhotoIndex: nextIndex\n        };\n      }\n      return state;\n    });\n  },\n\n  addVideoGeneration: (generation: VideoGeneration) => {\n    set((state) => ({\n      videoGenerations: [...state.videoGenerations, generation]\n    }));\n  },\n\n  updateVideoGeneration: (id: string, updates: Partial<VideoGeneration>) => {\n    set((state) => ({\n      videoGenerations: state.videoGenerations.map(gen =>\n        gen.id === id ? { ...gen, ...updates } : gen\n      )\n    }));\n  },\n\n  setLoading: (loading: boolean) => {\n    set({ isLoading: loading });\n  },\n\n  setError: (error: string | undefined) => {\n    set({ error });\n  },\n\n  initializePhotos: (photos: Photo[]) => {\n    set({ photos, currentPhotoIndex: 0 });\n  },\n\n  resetProgress: () => {\n    set((state) => ({\n      photos: state.photos.map((photo, index) => ({\n        ...photo,\n        isUnlocked: index === 0 // Only first photo unlocked\n      })),\n      currentPhotoIndex: 0,\n      videoGenerations: [],\n      error: undefined\n    }));\n  },\n\n  getVideoForPhoto: (photoId: string) => {\n    const { videoGenerations } = get();\n    return videoGenerations.find(gen => gen.photoId === photoId && gen.status === 'completed');\n  },\n\n  getCompletedVideos: () => {\n    const { videoGenerations } = get();\n    return videoGenerations.filter(gen => gen.status === 'completed');\n  },\n\n  getTotalProgress: () => {\n    const { photos } = get();\n    const unlockedCount = photos.filter(photo => photo.isUnlocked).length;\n    return Math.round((unlockedCount / photos.length) * 100);\n  }\n}),\n{\n  name: 'moments-forward-storage',\n  storage: createJSONStorage(() => localStorage),\n  partialize: (state) => ({\n    photos: state.photos,\n    currentPhotoIndex: state.currentPhotoIndex,\n    videoGenerations: state.videoGenerations,\n  }),\n}\n));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAkBA,2DAA2D;AAC3D,MAAM,eAAwB;IAC5B;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;CACD;AAEM,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,QAAQ;QACR,mBAAmB;QACnB,kBAAkB,EAAE;QACpB,WAAW;QACX,OAAO;QAEX,UAAU;QACV,iBAAiB,CAAC;YAChB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,SAAS,KAAK,QAAQ,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnE,IAAI;oBAAE,mBAAmB;gBAAM;YACjC;QACF;QAEA,iBAAiB;YACf,IAAI,CAAC;gBACH,MAAM,YAAY,MAAM,iBAAiB,GAAG;gBAC5C,IAAI,YAAY,MAAM,MAAM,CAAC,MAAM,EAAE;oBACnC,MAAM,gBAAgB,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,QAC7C,UAAU,YAAY;4BAAE,GAAG,KAAK;4BAAE,YAAY;wBAAK,IAAI;oBAEzD,OAAO;wBACL,QAAQ;wBACR,mBAAmB;oBACrB;gBACF;gBACA,OAAO;YACT;QACF;QAEA,oBAAoB,CAAC;YACnB,IAAI,CAAC,QAAU,CAAC;oBACd,kBAAkB;2BAAI,MAAM,gBAAgB;wBAAE;qBAAW;gBAC3D,CAAC;QACH;QAEA,uBAAuB,CAAC,IAAY;YAClC,IAAI,CAAC,QAAU,CAAC;oBACd,kBAAkB,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAA,MAC3C,IAAI,EAAE,KAAK,KAAK;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE7C,CAAC;QACH;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;gBAAQ,mBAAmB;YAAE;QACrC;QAEA,eAAe;YACb,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;4BAC1C,GAAG,KAAK;4BACR,YAAY,UAAU,EAAE,4BAA4B;wBACtD,CAAC;oBACD,mBAAmB;oBACnB,kBAAkB,EAAE;oBACpB,OAAO;gBACT,CAAC;QACH;QAEA,kBAAkB,CAAC;YACjB,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,OAAO,iBAAiB,IAAI,CAAC,CAAA,MAAO,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,KAAK;QAChF;QAEA,oBAAoB;YAClB,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,OAAO,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;QACvD;QAEA,kBAAkB;YAChB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,EAAE,MAAM;YACrE,OAAO,KAAK,KAAK,CAAC,AAAC,gBAAgB,OAAO,MAAM,GAAI;QACtD;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,QAAQ,MAAM,MAAM;YACpB,mBAAmB,MAAM,iBAAiB;YAC1C,kBAAkB,MAAM,gBAAgB;QAC1C,CAAC;AACH", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function validatePrompt(prompt: string, minLength: number = 20): {\n  isValid: boolean;\n  error?: string;\n} {\n  const trimmed = prompt.trim();\n  \n  if (trimmed.length === 0) {\n    return { isValid: false, error: 'Please enter a prompt' };\n  }\n  \n  if (trimmed.length < minLength) {\n    return { \n      isValid: false, \n      error: `Please write at least ${minLength} characters` \n    };\n  }\n  \n  return { isValid: true };\n}\n\nexport function createVideoGenerationRequest(photoId: string, prompt: string) {\n  return {\n    id: generateId(),\n    photoId,\n    prompt,\n    status: 'pending' as const,\n    createdAt: new Date(),\n  };\n}\n\n// Animation variants for Framer Motion\nexport const fadeInUp = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0 },\n  exit: { opacity: 0, y: -20 },\n};\n\nexport const fadeIn = {\n  initial: { opacity: 0 },\n  animate: { opacity: 1 },\n  exit: { opacity: 0 },\n};\n\nexport const scaleIn = {\n  initial: { opacity: 0, scale: 0.9 },\n  animate: { opacity: 1, scale: 1 },\n  exit: { opacity: 0, scale: 0.9 },\n};\n\nexport const slideInFromRight = {\n  initial: { opacity: 0, x: 20 },\n  animate: { opacity: 1, x: 0 },\n  exit: { opacity: 0, x: -20 },\n};\n\n// Stagger animation for lists\nexport const staggerContainer = {\n  animate: {\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n};\n\nexport const staggerItem = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0 },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,eAAe,MAAc;QAAE,YAAA,iEAAoB;IAIjE,MAAM,UAAU,OAAO,IAAI;IAE3B,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAwB;IAC1D;IAEA,IAAI,QAAQ,MAAM,GAAG,WAAW;QAC9B,OAAO;YACL,SAAS;YACT,OAAO,AAAC,yBAAkC,OAAV,WAAU;QAC5C;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,SAAS,6BAA6B,OAAe,EAAE,MAAc;IAC1E,OAAO;QACL,IAAI;QACJ;QACA;QACA,QAAQ;QACR,WAAW,IAAI;IACjB;AACF;AAGO,MAAM,WAAW;IACtB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;AAC7B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAE,SAAS;IAAE;IACtB,SAAS;QAAE,SAAS;IAAE;IACtB,MAAM;QAAE,SAAS;IAAE;AACrB;AAEO,MAAM,UAAU;IACrB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,SAAS;QAAE,SAAS;QAAG,OAAO;IAAE;IAChC,MAAM;QAAE,SAAS;QAAG,OAAO;IAAI;AACjC;AAEO,MAAM,mBAAmB;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;AAC7B;AAGO,MAAM,mBAAmB;IAC9B,SAAS;QACP,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;AAC9B", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/Timeline.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Lock, CheckCircle } from 'lucide-react';\nimport Image from 'next/image';\nimport { TimelineProps } from '@/types';\nimport { staggerContainer, staggerItem } from '@/lib/utils';\n\nconst Timeline: React.FC<TimelineProps> = ({ photos, currentIndex, onPhotoSelect }) => {\n  return (\n    <div className=\"w-full max-w-6xl mx-auto\">\n      {/* Desktop Timeline - Horizontal */}\n      <div className=\"hidden md:block\">\n        <div className=\"relative\">\n          {/* Timeline line */}\n          <div className=\"absolute top-1/2 left-0 right-0 h-0.5 bg-vintage-gold/30 transform -translate-y-1/2\" />\n\n          <motion.div\n            className=\"flex items-center justify-between px-8 relative z-10\"\n            variants={staggerContainer}\n            initial=\"initial\"\n            animate=\"animate\"\n          >\n            {photos.map((photo, index) => (\n              <motion.div\n                key={photo.id}\n                className=\"flex flex-col items-center\"\n                variants={staggerItem}\n              >\n                {/* Photo thumbnail */}\n                <motion.div\n                  className={`relative cursor-pointer transition-all duration-300 ${\n                    index === currentIndex\n                      ? 'w-32 h-32 golden-glow'\n                      : 'w-24 h-24 hover:scale-105'\n                  }`}\n                  onClick={() => photo.isUnlocked && onPhotoSelect(index)}\n                  whileHover={photo.isUnlocked ? { scale: 1.05 } : {}}\n                  whileTap={photo.isUnlocked ? { scale: 0.95 } : {}}\n                >\n                  <div className={`\n                    relative w-full h-full rounded-lg overflow-hidden vintage-frame\n                    ${!photo.isUnlocked ? 'opacity-50' : ''}\n                    ${index === currentIndex ? 'ring-4 ring-vintage-gold' : ''}\n                  `}>\n                    <Image\n                      src={photo.src}\n                      alt={photo.alt}\n                      fill\n                      className={`object-cover ${!photo.isUnlocked ? 'blur-sm' : ''}`}\n                      sizes=\"(max-width: 768px) 96px, 128px\"\n                    />\n\n                    {/* Lock/Unlock overlay */}\n                    {!photo.isUnlocked ? (\n                      <div className=\"absolute inset-0 flex items-center justify-center bg-black/20\">\n                        <Lock className=\"w-6 h-6 text-white drop-shadow-lg\" />\n                      </div>\n                    ) : index < currentIndex && (\n                      <motion.div\n                        className=\"absolute top-2 right-2\"\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        <CheckCircle className=\"w-5 h-5 text-vintage-gold drop-shadow-lg\" />\n                      </motion.div>\n                    )}\n\n                    {/* Progress indicator */}\n                    <div className=\"absolute bottom-2 left-2 right-2\">\n                      <div className=\"w-full h-1 bg-white/30 rounded-full overflow-hidden\">\n                        <motion.div\n                          className=\"h-full bg-vintage-gold\"\n                          initial={{ width: 0 }}\n                          animate={{\n                            width: photo.isUnlocked ? '100%' : '0%'\n                          }}\n                          transition={{ duration: 0.5, delay: 0.2 }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n\n                {/* Photo metadata */}\n                <div className=\"mt-4 text-center max-w-[120px]\">\n                  <p className=\"font-kalam text-sm text-vintage-brown font-medium\">\n                    {photo.date}\n                  </p>\n                  {photo.location && (\n                    <p className=\"font-inter text-xs text-vintage-brown/60 truncate\">\n                      {photo.location}\n                    </p>\n                  )}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Mobile Timeline - Horizontal Scroll */}\n      <div className=\"md:hidden\">\n        <div className=\"relative\">\n          {/* Timeline line */}\n          <div className=\"absolute top-1/2 left-4 right-4 h-0.5 bg-vintage-gold/30 transform -translate-y-1/2\" />\n\n          <div className=\"flex items-center space-x-6 overflow-x-auto px-4 py-4 relative z-10\">\n            {photos.map((photo, index) => (\n              <motion.div\n                key={photo.id}\n                className=\"flex flex-col items-center flex-shrink-0\"\n                variants={staggerItem}\n              >\n                {/* Photo thumbnail */}\n                <motion.div\n                  className={`relative cursor-pointer transition-all duration-300 ${\n                    index === currentIndex\n                      ? 'w-24 h-24 golden-glow'\n                      : 'w-20 h-20'\n                  }`}\n                  onClick={() => photo.isUnlocked && onPhotoSelect(index)}\n                  whileTap={photo.isUnlocked ? { scale: 0.95 } : {}}\n                >\n                  <div className={`\n                    relative w-full h-full rounded-lg overflow-hidden vintage-frame\n                    ${!photo.isUnlocked ? 'opacity-50' : ''}\n                    ${index === currentIndex ? 'ring-2 ring-vintage-gold' : ''}\n                  `}>\n                    <Image\n                      src={photo.src}\n                      alt={photo.alt}\n                      fill\n                      className={`object-cover ${!photo.isUnlocked ? 'blur-sm' : ''}`}\n                      sizes=\"96px\"\n                    />\n\n                    {!photo.isUnlocked ? (\n                      <div className=\"absolute inset-0 flex items-center justify-center bg-black/20\">\n                        <Lock className=\"w-4 h-4 text-white drop-shadow-lg\" />\n                      </div>\n                    ) : index < currentIndex && (\n                      <motion.div\n                        className=\"absolute top-1 right-1\"\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        <CheckCircle className=\"w-4 h-4 text-vintage-gold drop-shadow-lg\" />\n                      </motion.div>\n                    )}\n                  </div>\n                </motion.div>\n\n                {/* Photo info */}\n                <div className=\"mt-2 text-center max-w-[80px]\">\n                  <p className=\"font-kalam text-xs text-vintage-brown font-medium\">\n                    {photo.date}\n                  </p>\n                  {photo.location && (\n                    <p className=\"font-inter text-xs text-vintage-brown/60 truncate\">\n                      {photo.location}\n                    </p>\n                  )}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Timeline;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,WAAoC;QAAC,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE;IAChF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU,sHAAA,CAAA,mBAAgB;4BAC1B,SAAQ;4BACR,SAAQ;sCAEP,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,UAAU,sHAAA,CAAA,cAAW;;sDAGrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAW,AAAC,uDAIX,OAHC,UAAU,eACN,0BACA;4CAEN,SAAS,IAAM,MAAM,UAAU,IAAI,cAAc;4CACjD,YAAY,MAAM,UAAU,GAAG;gDAAE,OAAO;4CAAK,IAAI,CAAC;4CAClD,UAAU,MAAM,UAAU,GAAG;gDAAE,OAAO;4CAAK,IAAI,CAAC;sDAEhD,cAAA,6LAAC;gDAAI,WAAW,AAAC,8GAGb,OADA,CAAC,MAAM,UAAU,GAAG,eAAe,IAAG,0BACmB,OAAzD,UAAU,eAAe,6BAA6B,IAAG;;kEAE3D,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,MAAM,GAAG;wDACd,KAAK,MAAM,GAAG;wDACd,IAAI;wDACJ,WAAW,AAAC,gBAAkD,OAAnC,CAAC,MAAM,UAAU,GAAG,YAAY;wDAC3D,OAAM;;;;;;oDAIP,CAAC,MAAM,UAAU,iBAChB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;mGAEhB,QAAQ,8BACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,YAAY;4DAAE,UAAU;wDAAI;kEAE5B,cAAA,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAK3B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEACP,OAAO,MAAM,UAAU,GAAG,SAAS;gEACrC;gEACA,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,MAAM,IAAI;;;;;;gDAEZ,MAAM,QAAQ,kBACb,6LAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;;;;;;;;mCAnEhB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BA8EvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,UAAU,sHAAA,CAAA,cAAW;;sDAGrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAW,AAAC,uDAIX,OAHC,UAAU,eACN,0BACA;4CAEN,SAAS,IAAM,MAAM,UAAU,IAAI,cAAc;4CACjD,UAAU,MAAM,UAAU,GAAG;gDAAE,OAAO;4CAAK,IAAI,CAAC;sDAEhD,cAAA,6LAAC;gDAAI,WAAW,AAAC,8GAGb,OADA,CAAC,MAAM,UAAU,GAAG,eAAe,IAAG,0BACmB,OAAzD,UAAU,eAAe,6BAA6B,IAAG;;kEAE3D,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,MAAM,GAAG;wDACd,KAAK,MAAM,GAAG;wDACd,IAAI;wDACJ,WAAW,AAAC,gBAAkD,OAAnC,CAAC,MAAM,UAAU,GAAG,YAAY;wDAC3D,OAAM;;;;;;oDAGP,CAAC,MAAM,UAAU,iBAChB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;mGAEhB,QAAQ,8BACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,YAAY;4DAAE,UAAU;wDAAI;kEAE5B,cAAA,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAO/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,MAAM,IAAI;;;;;;gDAEZ,MAAM,QAAQ,kBACb,6LAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;;;;;;;;mCAnDhB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8D7B;KArKM;uCAuKS", "debugId": null}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/StoryContext.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { StoryContextProps } from '@/types';\n\nconst StoryContext: React.FC<StoryContextProps> = ({ story, signature, isVisible }) => {\n  const words = story.split(' ');\n\n  return (\n    <motion.div\n      className=\"relative bg-vintage-sepia/30 backdrop-blur-sm rounded-2xl p-8 border border-vintage-gold/20 shadow-lg max-w-2xl mx-auto\"\n      initial={{ opacity: 0, scale: 0.95 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Story text with typewriter effect */}\n      <div className=\"font-kalam text-lg text-vintage-brown leading-relaxed mb-6 text-center\">\n        {words.map((word, index) => (\n          <motion.span\n            key={index}\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{\n              duration: 0.1,\n              delay: index * 0.05\n            }}\n            className=\"inline-block mr-1\"\n          >\n            {word}\n          </motion.span>\n        ))}\n      </div>\n\n      {/* Brother's signature */}\n      <motion.div\n        className=\"text-center\"\n        initial={{ opacity: 0, y: 10 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{\n          duration: 0.5,\n          delay: words.length * 0.05 + 0.5\n        }}\n      >\n        <p className=\"font-kalam text-vintage-brown/80 italic text-lg\">\n          {signature}\n        </p>\n      </motion.div>\n\n      {/* Decorative elements */}\n      <div className=\"absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-vintage-gold/30 rounded-tl-lg\" />\n      <div className=\"absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-vintage-gold/30 rounded-tr-lg\" />\n      <div className=\"absolute bottom-4 left-4 w-8 h-8 border-l-2 border-b-2 border-vintage-gold/30 rounded-bl-lg\" />\n      <div className=\"absolute bottom-4 right-4 w-8 h-8 border-r-2 border-b-2 border-vintage-gold/30 rounded-br-lg\" />\n    </motion.div>\n  );\n};\n\nexport default StoryContext;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKA,MAAM,eAA4C;QAAC,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE;IAChF,MAAM,QAAQ,MAAM,KAAK,CAAC;IAE1B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBAEV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BACV,UAAU;4BACV,OAAO,QAAQ;wBACjB;wBACA,WAAU;kCAET;uBATI;;;;;;;;;;0BAeX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBACV,UAAU;oBACV,OAAO,MAAM,MAAM,GAAG,OAAO;gBAC/B;0BAEA,cAAA,6LAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;0BAKL,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KAlDM;uCAoDS", "debugId": null}}, {"offset": {"line": 781, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/InputComponent.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Camera, Send } from 'lucide-react';\nimport { InputComponentProps } from '@/types';\n\nconst InputComponent: React.FC<InputComponentProps> = ({\n  onSubmit,\n  isLoading,\n  placeholder = \"Imagine what happened in the next 10 seconds...\",\n  maxLength = 300,\n  minLength = 20\n}) => {\n  const [prompt, setPrompt] = useState('');\n  const [isFocused, setIsFocused] = useState(false);\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (prompt.trim().length >= minLength && !isLoading) {\n      onSubmit(prompt.trim());\n      setPrompt('');\n    }\n  };\n\n  const isValid = prompt.trim().length >= minLength;\n  const remainingChars = maxLength - prompt.length;\n\n  return (\n    <motion.div\n      className=\"w-full\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <form onSubmit={handleSubmit} className=\"space-y-4\">\n        {/* Input field */}\n        <div className=\"relative\">\n          <motion.div\n            className={`\n              relative bg-vintage-cream/50 backdrop-blur-sm rounded-2xl border-2 transition-all duration-300\n              ${isFocused ? 'border-vintage-gold shadow-lg' : 'border-vintage-gold/30'}\n            `}\n            whileFocus={{ scale: 1.02 }}\n          >\n            <textarea\n              value={prompt}\n              onChange={(e) => setPrompt(e.target.value)}\n              onFocus={() => setIsFocused(true)}\n              onBlur={() => setIsFocused(false)}\n              placeholder={placeholder}\n              maxLength={maxLength}\n              rows={4}\n              className=\"w-full p-4 md:p-6 bg-transparent resize-none outline-none font-inter text-vintage-brown placeholder-vintage-brown/50 text-base md:text-lg leading-relaxed\"\n              disabled={isLoading}\n            />\n            \n            {/* Film perforations character counter */}\n            <div className=\"absolute bottom-3 right-3 md:bottom-4 md:right-4 flex items-center space-x-1\">\n              {Array.from({ length: 5 }).map((_, i) => (\n                <div\n                  key={i}\n                  className={`w-2 h-2 rounded-full transition-colors duration-200 ${\n                    i < Math.floor((prompt.length / maxLength) * 5)\n                      ? 'bg-vintage-gold'\n                      : 'bg-vintage-brown/20'\n                  }`}\n                />\n              ))}\n              <span className=\"ml-2 font-poppins text-sm text-vintage-brown/60\">\n                {remainingChars}\n              </span>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Submit button */}\n        <div className=\"flex justify-center\">\n          <motion.button\n            type=\"submit\"\n            disabled={!isValid || isLoading}\n            className={`\n              flex items-center space-x-2 md:space-x-3 px-6 py-3 md:px-8 md:py-4 rounded-full font-poppins font-medium text-base md:text-lg transition-all duration-300 shadow-lg\n              ${isValid && !isLoading\n                ? 'bg-vintage-gold hover:bg-vintage-gold/90 text-vintage-brown hover:shadow-xl transform hover:scale-105'\n                : 'bg-vintage-brown/20 text-vintage-brown/40 cursor-not-allowed'\n              }\n            `}\n            whileHover={isValid && !isLoading ? { scale: 1.05 } : {}}\n            whileTap={isValid && !isLoading ? { scale: 0.95 } : {}}\n          >\n            {isLoading ? (\n              <>\n                <motion.div\n                  className=\"w-6 h-6 border-2 border-vintage-brown/30 border-t-vintage-brown rounded-full\"\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                />\n                <span>Creating magic...</span>\n              </>\n            ) : (\n              <>\n                <Camera className=\"w-6 h-6\" />\n                <span>Generate Video</span>\n                <Send className=\"w-5 h-5\" />\n              </>\n            )}\n          </motion.button>\n        </div>\n\n        {/* Validation message */}\n        {prompt.length > 0 && prompt.trim().length < minLength && (\n          <motion.p\n            className=\"text-center font-inter text-sm text-vintage-brown/60\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ duration: 0.3 }}\n          >\n            Please write at least {minLength} characters to create your video\n          </motion.p>\n        )}\n      </form>\n    </motion.div>\n  );\n};\n\nexport default InputComponent;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAOA,MAAM,iBAAgD;QAAC,EACrD,QAAQ,EACR,SAAS,EACT,cAAc,iDAAiD,EAC/D,YAAY,GAAG,EACf,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,OAAO,IAAI,GAAG,MAAM,IAAI,aAAa,CAAC,WAAW;YACnD,SAAS,OAAO,IAAI;YACpB,UAAU;QACZ;IACF;IAEA,MAAM,UAAU,OAAO,IAAI,GAAG,MAAM,IAAI;IACxC,MAAM,iBAAiB,YAAY,OAAO,MAAM;IAEhD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAU;;8BAEtC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,AAAC,iIAE+D,OAAvE,YAAY,kCAAkC,0BAAyB;wBAE3E,YAAY;4BAAE,OAAO;wBAAK;;0CAE1B,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,SAAS,IAAM,aAAa;gCAC5B,QAAQ,IAAM,aAAa;gCAC3B,aAAa;gCACb,WAAW;gCACX,MAAM;gCACN,WAAU;gCACV,UAAU;;;;;;0CAIZ,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;4CAEC,WAAW,AAAC,uDAIX,OAHC,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,MAAM,GAAG,YAAa,KACzC,oBACA;2CAJD;;;;;kDAQT,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;8BAOT,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,MAAK;wBACL,UAAU,CAAC,WAAW;wBACtB,WAAW,AAAC,sMAKT,OAHC,WAAW,CAAC,YACV,0GACA,gEACH;wBAEH,YAAY,WAAW,CAAC,YAAY;4BAAE,OAAO;wBAAK,IAAI,CAAC;wBACvD,UAAU,WAAW,CAAC,YAAY;4BAAE,OAAO;wBAAK,IAAI,CAAC;kCAEpD,0BACC;;8CACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;;;;;;8CAE9D,6LAAC;8CAAK;;;;;;;yDAGR;;8CACE,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;8CACN,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;gBAOvB,OAAO,MAAM,GAAG,KAAK,OAAO,IAAI,GAAG,MAAM,GAAG,2BAC3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;oBAAI;;wBAC7B;wBACwB;wBAAU;;;;;;;;;;;;;;;;;;AAM7C;GArHM;KAAA;uCAuHS", "debugId": null}}, {"offset": {"line": 1005, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/VideoPlayer.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Play, Pause, RotateCcw, Volume2 } from 'lucide-react';\nimport { VideoPlayerProps } from '@/types';\n\nconst VideoPlayer: React.FC<VideoPlayerProps> = ({ videoUrl, isVisible, onComplete }) => {\n  const videoRef = useRef<HTMLVideoElement>(null);\n\n  useEffect(() => {\n    if (isVisible) {\n      // Auto-complete after 5 seconds for demo purposes\n      const timer = setTimeout(() => {\n        handleVideoEnd();\n      }, 5000);\n\n      return () => clearTimeout(timer);\n    }\n  }, [isVisible]);\n\n  const handleVideoEnd = () => {\n    onComplete();\n  };\n\n  const handleReplay = () => {\n    const video = videoRef.current;\n    if (video) {\n      video.currentTime = 0;\n      video.play();\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"relative w-full max-w-2xl mx-auto\"\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.9 }}\n          transition={{ duration: 0.5 }}\n        >\n          {/* Video container with vintage styling */}\n          <div className=\"relative vintage-frame rounded-lg overflow-hidden bg-black\">\n            {/* Film grain overlay */}\n            <div className=\"film-grain absolute inset-0 z-10 pointer-events-none\" />\n            \n            {/* Placeholder for generated video */}\n            <div className=\"w-full aspect-video bg-gradient-to-br from-vintage-sepia to-vintage-gold flex items-center justify-center\">\n              <div className=\"text-center space-y-4\">\n                <motion.div\n                  className=\"w-16 h-16 mx-auto bg-white/20 rounded-full flex items-center justify-center\"\n                  animate={{\n                    scale: [1, 1.1, 1],\n                    opacity: [0.7, 1, 0.7],\n                  }}\n                  transition={{\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                  }}\n                >\n                  <Play className=\"w-8 h-8 text-white\" />\n                </motion.div>\n                <div className=\"text-white\">\n                  <p className=\"font-kalam text-lg\">Generated Video</p>\n                  <p className=\"font-inter text-sm opacity-80\">\n                    Your imagination brought to life\n                  </p>\n                </div>\n              </div>\n            </div>\n            \n            {/* Custom controls overlay */}\n            <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 z-20\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center space-x-4\">\n                  <button\n                    onClick={handleReplay}\n                    className=\"flex items-center justify-center w-10 h-10 bg-vintage-gold/20 hover:bg-vintage-gold/30 rounded-full transition-colors duration-200\"\n                  >\n                    <RotateCcw className=\"w-5 h-5 text-white\" />\n                  </button>\n                  \n                  <Volume2 className=\"w-5 h-5 text-white/80\" />\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"text-white/80 font-poppins text-sm\">\n                    AI Generated Memory\n                  </div>\n                  <div className=\"w-2 h-2 bg-vintage-gold rounded-full animate-pulse\" />\n                </div>\n              </div>\n            </div>\n            \n            {/* Film projector loading animation when video is loading */}\n            <motion.div\n              className=\"absolute inset-0 flex items-center justify-center bg-black/50 z-30\"\n              initial={{ opacity: 1 }}\n              animate={{ opacity: 0 }}\n              transition={{ duration: 1, delay: 0.5 }}\n            >\n              <div className=\"flex flex-col items-center space-y-4\">\n                <motion.div\n                  className=\"w-16 h-16 border-4 border-vintage-gold/30 border-t-vintage-gold rounded-full\"\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n                />\n                <p className=\"text-white font-poppins text-sm\">\n                  Loading your memory...\n                </p>\n              </div>\n            </motion.div>\n          </div>\n          \n          {/* Video metadata */}\n          <motion.div\n            className=\"mt-4 text-center\"\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n          >\n            <p className=\"font-kalam text-vintage-brown/80 text-lg\">\n              ✨ Your imagination brought to life ✨\n            </p>\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default VideoPlayer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;;;AAJA;;;;AAOA,MAAM,cAA0C;QAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE;;IAClF,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW;gBACb,kDAAkD;gBAClD,MAAM,QAAQ;mDAAW;wBACvB;oBACF;kDAAG;gBAEH;6CAAO,IAAM,aAAa;;YAC5B;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,iBAAiB;QACrB;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,MAAM,WAAW,GAAG;YACpB,MAAM,IAAI;QACZ;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,MAAM;wCACR;kDAEA,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;sCAQnD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAGvB,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;kDAGrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAqC;;;;;;0DAGpD,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;sCAEtC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,QAAQ;wCAAI;wCACvB,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,MAAM;wCAAS;;;;;;kDAE9D,6LAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;;;;;;8BAQrD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,6LAAC;wBAAE,WAAU;kCAA2C;;;;;;;;;;;;;;;;;;;;;;AAQpE;GA7HM;KAAA;uCA+HS", "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/LoadingAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { LoadingState } from '@/types';\n\ninterface LoadingAnimationProps {\n  loadingState: LoadingState;\n  isVisible: boolean;\n}\n\nconst LoadingAnimation: React.FC<LoadingAnimationProps> = ({ loadingState, isVisible }) => {\n  const getLoadingContent = () => {\n    switch (loadingState.type) {\n      case 'photo-unlock':\n        return (\n          <div className=\"flex flex-col items-center space-y-6\">\n            {/* Sparkle animation */}\n            <div className=\"relative w-24 h-24\">\n              {Array.from({ length: 8 }).map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-2 h-2 bg-vintage-gold rounded-full\"\n                  style={{\n                    left: '50%',\n                    top: '50%',\n                    transformOrigin: '0 40px',\n                  }}\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [0, 1, 0],\n                    opacity: [0, 1, 0],\n                  }}\n                  transition={{\n                    duration: 2,\n                    repeat: Infinity,\n                    delay: i * 0.25,\n                    ease: \"easeInOut\",\n                  }}\n                />\n              ))}\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <motion.div\n                  className=\"w-8 h-8 bg-vintage-gold rounded-full\"\n                  animate={{\n                    scale: [1, 1.2, 1],\n                    opacity: [0.5, 1, 0.5],\n                  }}\n                  transition={{\n                    duration: 1.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                  }}\n                />\n              </div>\n            </div>\n            <p className=\"font-kalam text-vintage-brown text-lg text-center\">\n              {loadingState.message}\n            </p>\n          </div>\n        );\n\n      case 'video-generation':\n        return (\n          <div className=\"flex flex-col items-center space-y-6\">\n            {/* Film reel animation */}\n            <div className=\"relative\">\n              <motion.div\n                className=\"w-20 h-20 border-4 border-vintage-gold/30 rounded-full\"\n                animate={{ rotate: 360 }}\n                transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n              >\n                <div className=\"absolute inset-2 border-2 border-vintage-gold/50 rounded-full\">\n                  <div className=\"absolute inset-2 border border-vintage-gold rounded-full\">\n                    {/* Film perforations */}\n                    {Array.from({ length: 12 }).map((_, i) => (\n                      <div\n                        key={i}\n                        className=\"absolute w-1 h-2 bg-vintage-gold/60 rounded-sm\"\n                        style={{\n                          left: '50%',\n                          top: '50%',\n                          transformOrigin: '0 20px',\n                          transform: `translate(-50%, -50%) rotate(${i * 30}deg)`,\n                        }}\n                      />\n                    ))}\n                  </div>\n                </div>\n              </motion.div>\n              \n              {/* Progress dots */}\n              <div className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-1\">\n                {Array.from({ length: 3 }).map((_, i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-2 h-2 bg-vintage-gold rounded-full\"\n                    animate={{\n                      scale: [1, 1.5, 1],\n                      opacity: [0.3, 1, 0.3],\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.2,\n                      ease: \"easeInOut\",\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n            \n            <div className=\"text-center space-y-2\">\n              <p className=\"font-kalam text-vintage-brown text-lg\">\n                {loadingState.message}\n              </p>\n              <p className=\"font-inter text-vintage-brown/60 text-sm\">\n                This may take a few moments...\n              </p>\n            </div>\n          </div>\n        );\n\n      case 'page-transition':\n        return (\n          <div className=\"flex flex-col items-center space-y-4\">\n            {/* Gentle fade animation */}\n            <motion.div\n              className=\"w-16 h-16 bg-vintage-gold/20 rounded-full\"\n              animate={{\n                scale: [1, 1.1, 1],\n                opacity: [0.5, 1, 0.5],\n              }}\n              transition={{\n                duration: 1.5,\n                repeat: Infinity,\n                ease: \"easeInOut\",\n              }}\n            />\n            <p className=\"font-inter text-vintage-brown/80\">\n              {loadingState.message}\n            </p>\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"flex items-center space-x-3\">\n            <motion.div\n              className=\"w-6 h-6 border-2 border-vintage-gold/30 border-t-vintage-gold rounded-full\"\n              animate={{ rotate: 360 }}\n              transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n            />\n            <p className=\"font-inter text-vintage-brown\">\n              {loadingState.message}\n            </p>\n          </div>\n        );\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      className=\"fixed inset-0 bg-vintage-off-white/80 backdrop-blur-sm flex items-center justify-center z-50\"\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      exit={{ opacity: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      <motion.div\n        className=\"bg-vintage-cream/90 backdrop-blur-sm rounded-2xl p-8 border border-vintage-gold/20 shadow-xl\"\n        initial={{ scale: 0.9, opacity: 0 }}\n        animate={{ scale: 1, opacity: 1 }}\n        exit={{ scale: 0.9, opacity: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        {getLoadingContent()}\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default LoadingAnimation;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUA,MAAM,mBAAoD;QAAC,EAAE,YAAY,EAAE,SAAS,EAAE;IACpF,MAAM,oBAAoB;QACxB,OAAQ,aAAa,IAAI;YACvB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;gCACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,MAAM;4CACN,KAAK;4CACL,iBAAiB;wCACnB;wCACA,SAAS;4CACP,QAAQ;gDAAC;gDAAG;6CAAI;4CAChB,OAAO;gDAAC;gDAAG;gDAAG;6CAAE;4CAChB,SAAS;gDAAC;gDAAG;gDAAG;6CAAE;wCACpB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;4CACX,MAAM;wCACR;uCAjBK;;;;;8CAoBT,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,MAAM;wCACR;;;;;;;;;;;;;;;;;sCAIN,6LAAC;4BAAE,WAAU;sCACV,aAAa,OAAO;;;;;;;;;;;;YAK7B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;8CAE5D,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAEZ,MAAM,IAAI,CAAC;gDAAE,QAAQ;4CAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,6LAAC;oDAEC,WAAU;oDACV,OAAO;wDACL,MAAM;wDACN,KAAK;wDACL,iBAAiB;wDACjB,WAAW,AAAC,gCAAsC,OAAP,IAAI,IAAG;oDACpD;mDAPK;;;;;;;;;;;;;;;;;;;;8CAef,6LAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,SAAS;gDACP,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;gDAClB,SAAS;oDAAC;oDAAK;oDAAG;iDAAI;4CACxB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,OAAO,IAAI;gDACX,MAAM;4CACR;2CAXK;;;;;;;;;;;;;;;;sCAiBb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,aAAa,OAAO;;;;;;8CAEvB,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;YAOhE,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;gCAClB,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;4BACxB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,MAAM;4BACR;;;;;;sCAEF,6LAAC;4BAAE,WAAU;sCACV,aAAa,OAAO;;;;;;;;;;;;YAK7B;gBACE,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,QAAQ;4BAAI;4BACvB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;gCAAU,MAAM;4BAAS;;;;;;sCAE9D,6LAAC;4BAAE,WAAU;sCACV,aAAa,OAAO;;;;;;;;;;;;QAI/B;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,OAAO;gBAAK,SAAS;YAAE;YAClC,SAAS;gBAAE,OAAO;gBAAG,SAAS;YAAE;YAChC,MAAM;gBAAE,OAAO;gBAAK,SAAS;YAAE;YAC/B,YAAY;gBAAE,UAAU;YAAI;sBAE3B;;;;;;;;;;;AAIT;KA3KM;uCA6KS", "debugId": null}}, {"offset": {"line": 1694, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/UnlockAnimation.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';\n\ninterface UnlockAnimationProps {\n  isVisible: boolean;\n  onComplete: () => void;\n  photoIndex: number;\n}\n\nconst UnlockAnimation: React.FC<UnlockAnimationProps> = ({ \n  isVisible, \n  onComplete, \n  photoIndex \n}) => {\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed inset-0 bg-vintage-off-white/80 backdrop-blur-sm flex items-center justify-center z-50\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          onAnimationComplete={() => {\n            setTimeout(onComplete, 2000); // Show for 2 seconds\n          }}\n        >\n          <motion.div\n            className=\"text-center space-y-8\"\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.8, opacity: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            {/* Sparkle burst animation */}\n            <div className=\"relative w-32 h-32 mx-auto\">\n              {/* Central heart */}\n              <motion.div\n                className=\"absolute inset-0 flex items-center justify-center\"\n                initial={{ scale: 0 }}\n                animate={{ scale: [0, 1.2, 1] }}\n                transition={{ duration: 0.8, times: [0, 0.6, 1] }}\n              >\n                <Heart className=\"w-16 h-16 text-vintage-gold fill-vintage-gold\" />\n              </motion.div>\n\n              {/* Sparkles */}\n              {Array.from({ length: 12 }).map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-3 h-3\"\n                  style={{\n                    left: '50%',\n                    top: '50%',\n                    transformOrigin: '0 0',\n                  }}\n                  initial={{ \n                    scale: 0, \n                    rotate: i * 30,\n                    x: 0,\n                    y: 0,\n                  }}\n                  animate={{ \n                    scale: [0, 1, 0],\n                    x: Math.cos((i * 30) * Math.PI / 180) * 60,\n                    y: Math.sin((i * 30) * Math.PI / 180) * 60,\n                  }}\n                  transition={{ \n                    duration: 1.5,\n                    delay: 0.3 + (i * 0.05),\n                    ease: \"easeOut\"\n                  }}\n                >\n                  <Sparkles className=\"w-3 h-3 text-vintage-gold\" />\n                </motion.div>\n              ))}\n\n              {/* Ripple effect */}\n              {Array.from({ length: 3 }).map((_, i) => (\n                <motion.div\n                  key={`ripple-${i}`}\n                  className=\"absolute inset-0 border-2 border-vintage-gold/30 rounded-full\"\n                  initial={{ scale: 0, opacity: 1 }}\n                  animate={{ \n                    scale: [0, 2, 3],\n                    opacity: [1, 0.5, 0]\n                  }}\n                  transition={{ \n                    duration: 2,\n                    delay: i * 0.3,\n                    ease: \"easeOut\"\n                  }}\n                />\n              ))}\n            </div>\n\n            {/* Success message */}\n            <motion.div\n              className=\"space-y-4\"\n              initial={{ y: 20, opacity: 0 }}\n              animate={{ y: 0, opacity: 1 }}\n              transition={{ duration: 0.5, delay: 0.5 }}\n            >\n              <h2 className=\"font-playfair text-3xl text-vintage-brown\">\n                Memory Unlocked!\n              </h2>\n              <p className=\"font-kalam text-lg text-vintage-brown/80\">\n                Photo {photoIndex + 1} is now available\n              </p>\n              <motion.p\n                className=\"font-inter text-vintage-brown/60\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ duration: 0.5, delay: 1 }}\n              >\n                Your story brought this moment to life ✨\n              </motion.p>\n            </motion.div>\n\n            {/* Progress celebration */}\n            <motion.div\n              className=\"flex justify-center space-x-2\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 1.2 }}\n            >\n              {Array.from({ length: 5 }).map((_, i) => (\n                <motion.div\n                  key={i}\n                  className={`w-3 h-3 rounded-full ${\n                    i <= photoIndex ? 'bg-vintage-gold' : 'bg-vintage-brown/20'\n                  }`}\n                  initial={{ scale: 0 }}\n                  animate={{ scale: 1 }}\n                  transition={{ \n                    duration: 0.3, \n                    delay: 1.2 + (i * 0.1) \n                  }}\n                />\n              ))}\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default UnlockAnimation;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AAHA;;;;AAWA,MAAM,kBAAkD;QAAC,EACvD,SAAS,EACT,UAAU,EACV,UAAU,EACX;IACC,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,qBAAqB;gBACnB,WAAW,YAAY,OAAO,qBAAqB;YACrD;sBAEA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,UAAU;gBAAI;;kCAG5B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC9B,YAAY;oCAAE,UAAU;oCAAK,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;0CAEhD,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;4BAIlB,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCACL,MAAM;wCACN,KAAK;wCACL,iBAAiB;oCACnB;oCACA,SAAS;wCACP,OAAO;wCACP,QAAQ,IAAI;wCACZ,GAAG;wCACH,GAAG;oCACL;oCACA,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;wCAChB,GAAG,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,KAAK,EAAE,GAAG,OAAO;wCACxC,GAAG,KAAK,GAAG,CAAC,AAAC,IAAI,KAAM,KAAK,EAAE,GAAG,OAAO;oCAC1C;oCACA,YAAY;wCACV,UAAU;wCACV,OAAO,MAAO,IAAI;wCAClB,MAAM;oCACR;8CAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;mCAxBf;;;;;4BA6BR,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,OAAO;wCAAG,SAAS;oCAAE;oCAChC,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;wCAChB,SAAS;4CAAC;4CAAG;4CAAK;yCAAE;oCACtB;oCACA,YAAY;wCACV,UAAU;wCACV,OAAO,IAAI;wCACX,MAAM;oCACR;mCAXK,AAAC,UAAW,OAAF;;;;;;;;;;;kCAiBrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,6LAAC;gCAAE,WAAU;;oCAA2C;oCAC/C,aAAa;oCAAE;;;;;;;0CAExB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAE;0CACvC;;;;;;;;;;;;kCAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAEvC,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAW,AAAC,wBAEX,OADC,KAAK,aAAa,oBAAoB;gCAExC,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCACV,UAAU;oCACV,OAAO,MAAO,IAAI;gCACpB;+BATK;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBvB;KAxIM;uCA0IS", "debugId": null}}, {"offset": {"line": 1978, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/hooks/useVideoGeneration.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\nimport { useAppStore } from '@/stores/appStore';\nimport { generateId, sleep } from '@/lib/utils';\nimport { VideoGeneration } from '@/types';\n\nexport const useVideoGeneration = () => {\n  const [isGenerating, setIsGenerating] = useState(false);\n  const { addVideoGeneration, updateVideoGeneration, unlockNextPhoto } = useAppStore();\n\n  const generateVideo = useCallback(async (photoId: string, prompt: string): Promise<string | null> => {\n    setIsGenerating(true);\n\n    // Create video generation record\n    const generation: VideoGeneration = {\n      id: generateId(),\n      photoId,\n      prompt,\n      status: 'pending',\n      createdAt: new Date(),\n    };\n\n    addVideoGeneration(generation);\n\n    try {\n      // Update status to generating\n      updateVideoGeneration(generation.id, { status: 'generating' });\n\n      // Call our API endpoint for video generation\n      const response = await fetch('/api/generate-video', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          imageUrl: `/photos/photo${photoId}.svg`, // Use the photo as base image\n          prompt,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to generate video');\n      }\n\n      const data = await response.json();\n      const mockVideoUrl = data.videoUrl;\n      \n      updateVideoGeneration(generation.id, {\n        status: 'completed',\n        videoUrl: mockVideoUrl,\n      });\n\n      // Unlock next photo after successful generation\n      unlockNextPhoto();\n\n      setIsGenerating(false);\n      return mockVideoUrl;\n\n    } catch (error) {\n      console.error('Video generation failed:', error);\n      \n      updateVideoGeneration(generation.id, { status: 'failed' });\n      setIsGenerating(false);\n      return null;\n    }\n  }, [addVideoGeneration, updateVideoGeneration, unlockNextPhoto]);\n\n  return {\n    generateVideo,\n    isGenerating,\n  };\n};\n\n// Hook for RunwayML API integration (to be implemented)\nexport const useRunwayML = () => {\n  const generateVideoWithRunway = useCallback(async (\n    imageUrl: string,\n    prompt: string\n  ): Promise<string | null> => {\n    try {\n      // TODO: Implement actual RunwayML API integration\n      // This would involve:\n      // 1. Uploading the image to RunwayML\n      // 2. Creating a generation request with the prompt\n      // 3. Polling for completion\n      // 4. Returning the generated video URL\n\n      const response = await fetch('/api/generate-video', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          imageUrl,\n          prompt,\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to generate video');\n      }\n\n      const data = await response.json();\n      return data.videoUrl;\n\n    } catch (error) {\n      console.error('RunwayML API error:', error);\n      return null;\n    }\n  }, []);\n\n  return {\n    generateVideoWithRunway,\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAGO,MAAM,qBAAqB;;IAChC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,kBAAkB,EAAE,qBAAqB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAEjF,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO,SAAiB;YACxD,gBAAgB;YAEhB,iCAAiC;YACjC,MAAM,aAA8B;gBAClC,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;gBACb;gBACA;gBACA,QAAQ;gBACR,WAAW,IAAI;YACjB;YAEA,mBAAmB;YAEnB,IAAI;gBACF,8BAA8B;gBAC9B,sBAAsB,WAAW,EAAE,EAAE;oBAAE,QAAQ;gBAAa;gBAE5D,6CAA6C;gBAC7C,MAAM,WAAW,MAAM,MAAM,uBAAuB;oBAClD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,UAAU,AAAC,gBAAuB,OAAR,SAAQ;wBAClC;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,eAAe,KAAK,QAAQ;gBAElC,sBAAsB,WAAW,EAAE,EAAE;oBACnC,QAAQ;oBACR,UAAU;gBACZ;gBAEA,gDAAgD;gBAChD;gBAEA,gBAAgB;gBAChB,OAAO;YAET,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAE1C,sBAAsB,WAAW,EAAE,EAAE;oBAAE,QAAQ;gBAAS;gBACxD,gBAAgB;gBAChB,OAAO;YACT;QACF;wDAAG;QAAC;QAAoB;QAAuB;KAAgB;IAE/D,OAAO;QACL;QACA;IACF;AACF;GAjEa;;QAE4D,4HAAA,CAAA,cAAW;;;AAkE7E,MAAM,cAAc;;IACzB,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,OAC1C,UACA;YAEA,IAAI;gBACF,kDAAkD;gBAClD,sBAAsB;gBACtB,qCAAqC;gBACrC,mDAAmD;gBACnD,4BAA4B;gBAC5B,uCAAuC;gBAEvC,MAAM,WAAW,MAAM,MAAM,uBAAuB;oBAClD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO,KAAK,QAAQ;YAEtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,OAAO;YACT;QACF;2DAAG,EAAE;IAEL,OAAO;QACL;IACF;AACF;IAxCa", "debugId": null}}, {"offset": {"line": 2104, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/PhotoStage.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Image from 'next/image';\nimport { PhotoStageProps } from '@/types';\nimport StoryContext from './StoryContext';\nimport InputComponent from './InputComponent';\nimport VideoPlayer from './VideoPlayer';\nimport LoadingAnimation from './LoadingAnimation';\nimport UnlockAnimation from './UnlockAnimation';\nimport { useVideoGeneration } from '@/hooks/useVideoGeneration';\nimport { useAppStore } from '@/stores/appStore';\n\nconst PhotoStage: React.FC<PhotoStageProps> = ({ photo, isActive, onPromptSubmit }) => {\n  const [showStory, setShowStory] = useState(false);\n  const [showInput, setShowInput] = useState(false);\n  const [generatedVideoUrl, setGeneratedVideoUrl] = useState<string | null>(null);\n  const [showVideo, setShowVideo] = useState(false);\n  const [showUnlockAnimation, setShowUnlockAnimation] = useState(false);\n\n  const { generateVideo, isGenerating } = useVideoGeneration();\n  const { currentPhotoIndex } = useAppStore();\n\n  const handlePhotoClick = () => {\n    if (!showStory) {\n      setShowStory(true);\n    } else if (!showInput) {\n      setShowInput(true);\n    }\n  };\n\n  const handlePromptSubmit = async (prompt: string) => {\n    setShowInput(false);\n\n    // Generate video\n    const videoUrl = await generateVideo(photo.id, prompt);\n\n    if (videoUrl) {\n      setGeneratedVideoUrl(videoUrl);\n      setShowVideo(true);\n    }\n\n    onPromptSubmit(prompt);\n  };\n\n  const handleVideoComplete = () => {\n    setShowVideo(false);\n    setShowStory(false);\n    setGeneratedVideoUrl(null);\n\n    // Show unlock animation for next photo\n    setShowUnlockAnimation(true);\n  };\n\n  const handleUnlockComplete = () => {\n    setShowUnlockAnimation(false);\n  };\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n      {/* Main Photo Display */}\n      <div className=\"flex flex-col items-center space-y-8\">\n        <motion.div\n          className=\"relative cursor-pointer group w-full max-w-2xl\"\n          onClick={handlePhotoClick}\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          layout\n        >\n          <div className=\"relative w-full vintage-frame rounded-lg overflow-hidden\">\n            <Image\n              src={photo.src}\n              alt={photo.alt}\n              width={800}\n              height={600}\n              className=\"w-full h-auto object-cover\"\n              priority\n            />\n\n            {/* Film grain overlay */}\n            <div className=\"film-grain absolute inset-0\" />\n\n            {/* Photo metadata overlay */}\n            <div className=\"absolute bottom-4 left-4 bg-black/50 text-white px-3 py-2 rounded-lg backdrop-blur-sm\">\n              <p className=\"font-kalam text-sm\">\n                {photo.date}\n              </p>\n              {photo.location && (\n                <p className=\"font-inter text-xs opacity-80\">\n                  {photo.location}\n                </p>\n              )}\n            </div>\n\n            {/* Interaction hint */}\n            {!showStory && (\n              <motion.div\n                className=\"absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 0 }}\n                whileHover={{ opacity: 1 }}\n              >\n                <div className=\"bg-vintage-gold text-vintage-brown px-6 py-3 rounded-full font-poppins font-medium\">\n                  Click to begin the story\n                </div>\n              </motion.div>\n            )}\n          </div>\n        </motion.div>\n\n        {/* Story Context */}\n        <AnimatePresence>\n          {showStory && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.5 }}\n              className=\"w-full max-w-2xl\"\n            >\n              <StoryContext\n                story={photo.contextStory}\n                signature={photo.brotherSignature}\n                isVisible={showStory}\n              />\n\n              {/* Continue button */}\n              {!showInput && (\n                <motion.div\n                  className=\"text-center mt-8\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 1 }}\n                >\n                  <button\n                    onClick={() => setShowInput(true)}\n                    className=\"bg-vintage-gold hover:bg-vintage-gold/90 text-vintage-brown px-8 py-3 rounded-full font-poppins font-medium transition-colors duration-200 shadow-lg hover:shadow-xl\"\n                  >\n                    What happened next?\n                  </button>\n                </motion.div>\n              )}\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Input Component */}\n        <AnimatePresence>\n          {showInput && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.5 }}\n              className=\"w-full max-w-2xl\"\n            >\n              <InputComponent\n                onSubmit={handlePromptSubmit}\n                isLoading={isGenerating}\n                placeholder=\"Imagine what happened in the next 10 seconds...\"\n                maxLength={300}\n                minLength={20}\n              />\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Video Player */}\n        <AnimatePresence>\n          {showVideo && generatedVideoUrl && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.5 }}\n              className=\"w-full max-w-2xl\"\n            >\n              <VideoPlayer\n                videoUrl={generatedVideoUrl}\n                isVisible={showVideo}\n                onComplete={handleVideoComplete}\n              />\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n      {/* Loading Animation */}\n      <LoadingAnimation\n        loadingState={{\n          type: 'video-generation',\n          message: 'Creating your magical moment...'\n        }}\n        isVisible={isGenerating}\n      />\n\n      {/* Unlock Animation */}\n      <UnlockAnimation\n        isVisible={showUnlockAnimation}\n        onComplete={handleUnlockComplete}\n        photoIndex={currentPhotoIndex}\n      />\n      </div>\n    </div>\n  );\n};\n\nexport default PhotoStage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAcA,MAAM,aAAwC;QAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE;;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IACzD,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IAExC,MAAM,mBAAmB;QACvB,IAAI,CAAC,WAAW;YACd,aAAa;QACf,OAAO,IAAI,CAAC,WAAW;YACrB,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,aAAa;QAEb,iBAAiB;QACjB,MAAM,WAAW,MAAM,cAAc,MAAM,EAAE,EAAE;QAE/C,IAAI,UAAU;YACZ,qBAAqB;YACrB,aAAa;QACf;QAEA,eAAe;IACjB;IAEA,MAAM,sBAAsB;QAC1B,aAAa;QACb,aAAa;QACb,qBAAqB;QAErB,uCAAuC;QACvC,uBAAuB;IACzB;IAEA,MAAM,uBAAuB;QAC3B,uBAAuB;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;oBACT,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,UAAU;wBAAE,OAAO;oBAAK;oBACxB,MAAM;8BAEN,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,MAAM,GAAG;gCACd,KAAK,MAAM,GAAG;gCACd,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,QAAQ;;;;;;0CAIV,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,MAAM,IAAI;;;;;;oCAEZ,MAAM,QAAQ,kBACb,6LAAC;wCAAE,WAAU;kDACV,MAAM,QAAQ;;;;;;;;;;;;4BAMpB,CAAC,2BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,SAAS;gCAAE;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;8CAAqF;;;;;;;;;;;;;;;;;;;;;;8BAS5G,6LAAC,4LAAA,CAAA,kBAAe;8BACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,6LAAC,qIAAA,CAAA,UAAY;gCACX,OAAO,MAAM,YAAY;gCACzB,WAAW,MAAM,gBAAgB;gCACjC,WAAW;;;;;;4BAIZ,CAAC,2BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAE;0CAEvB,cAAA,6LAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;8BAUX,6LAAC,4LAAA,CAAA,kBAAe;8BACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,6LAAC,uIAAA,CAAA,UAAc;4BACb,UAAU;4BACV,WAAW;4BACX,aAAY;4BACZ,WAAW;4BACX,WAAW;;;;;;;;;;;;;;;;8BAOnB,6LAAC,4LAAA,CAAA,kBAAe;8BACb,aAAa,mCACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAEV,cAAA,6LAAC,oIAAA,CAAA,UAAW;4BACV,UAAU;4BACV,WAAW;4BACX,YAAY;;;;;;;;;;;;;;;;8BAOtB,6LAAC,yIAAA,CAAA,UAAgB;oBACf,cAAc;wBACZ,MAAM;wBACN,SAAS;oBACX;oBACA,WAAW;;;;;;8BAIb,6LAAC,wIAAA,CAAA,UAAe;oBACd,WAAW;oBACX,YAAY;oBACZ,YAAY;;;;;;;;;;;;;;;;;AAKpB;GA/LM;;QAOoC,qIAAA,CAAA,qBAAkB;QAC5B,4HAAA,CAAA,cAAW;;;KARrC;uCAiMS", "debugId": null}}, {"offset": {"line": 2457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/ProgressIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useAppStore } from '@/stores/appStore';\n\nconst ProgressIndicator: React.FC = () => {\n  const { photos, getTotalProgress, getCompletedVideos } = useAppStore();\n  const progress = getTotalProgress();\n  const completedVideos = getCompletedVideos();\n  const unlockedPhotos = photos.filter(photo => photo.isUnlocked).length;\n\n  return (\n    <div className=\"fixed top-4 right-4 z-30\">\n      <motion.div\n        className=\"bg-vintage-cream/90 backdrop-blur-sm rounded-2xl p-3 md:p-4 border border-vintage-gold/20 shadow-lg\"\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <div className=\"flex items-center space-x-3 md:space-x-4\">\n          {/* Circular progress */}\n          <div className=\"relative w-10 h-10 md:w-12 md:h-12\">\n            <svg className=\"w-10 h-10 md:w-12 md:h-12 transform -rotate-90\" viewBox=\"0 0 36 36\">\n              {/* Background circle */}\n              <path\n                className=\"text-vintage-brown/20\"\n                stroke=\"currentColor\"\n                strokeWidth=\"3\"\n                fill=\"transparent\"\n                d=\"M18 2.0845\n                  a 15.9155 15.9155 0 0 1 0 31.831\n                  a 15.9155 15.9155 0 0 1 0 -31.831\"\n              />\n              {/* Progress circle */}\n              <motion.path\n                className=\"text-vintage-gold\"\n                stroke=\"currentColor\"\n                strokeWidth=\"3\"\n                fill=\"transparent\"\n                strokeLinecap=\"round\"\n                d=\"M18 2.0845\n                  a 15.9155 15.9155 0 0 1 0 31.831\n                  a 15.9155 15.9155 0 0 1 0 -31.831\"\n                initial={{ strokeDasharray: \"0 100\" }}\n                animate={{ strokeDasharray: `${progress} 100` }}\n                transition={{ duration: 1, ease: \"easeInOut\" }}\n              />\n            </svg>\n            \n            {/* Progress text */}\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <span className=\"text-xs md:text-sm font-poppins font-semibold text-vintage-brown\">\n                {progress}%\n              </span>\n            </div>\n          </div>\n\n          {/* Progress details */}\n          <div className=\"text-xs md:text-sm hidden sm:block\">\n            <div className=\"font-poppins font-medium text-vintage-brown\">\n              {unlockedPhotos} of {photos.length}\n            </div>\n            <div className=\"font-inter text-vintage-brown/60\">\n              memories unlocked\n            </div>\n            {completedVideos.length > 0 && (\n              <div className=\"font-inter text-vintage-gold text-xs mt-1\">\n                {completedVideos.length} videos created\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Mini timeline */}\n        <div className=\"mt-3 flex space-x-1\">\n          {photos.map((photo, index) => (\n            <motion.div\n              key={photo.id}\n              className={`w-2 h-2 rounded-full ${\n                photo.isUnlocked \n                  ? 'bg-vintage-gold' \n                  : 'bg-vintage-brown/20'\n              }`}\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n            />\n          ))}\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ProgressIndicator;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,oBAA8B;;IAClC,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IACnE,MAAM,WAAW;IACjB,MAAM,kBAAkB;IACxB,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,EAAE,MAAM;IAEtE,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAiD,SAAQ;;sDAEtE,6LAAC;4CACC,WAAU;4CACV,QAAO;4CACP,aAAY;4CACZ,MAAK;4CACL,GAAE;;;;;;sDAKJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAU;4CACV,QAAO;4CACP,aAAY;4CACZ,MAAK;4CACL,eAAc;4CACd,GAAE;4CAGF,SAAS;gDAAE,iBAAiB;4CAAQ;4CACpC,SAAS;gDAAE,iBAAiB,AAAC,GAAW,OAAT,UAAS;4CAAM;4CAC9C,YAAY;gDAAE,UAAU;gDAAG,MAAM;4CAAY;;;;;;;;;;;;8CAKjD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;4CACb;4CAAS;;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCACZ;wCAAe;wCAAK,OAAO,MAAM;;;;;;;8CAEpC,6LAAC;oCAAI,WAAU;8CAAmC;;;;;;gCAGjD,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;oCAAI,WAAU;;wCACZ,gBAAgB,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;8BAOhC,6LAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAW,AAAC,wBAIX,OAHC,MAAM,UAAU,GACZ,oBACA;4BAEN,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;2BAR3C,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;AAe3B;GAvFM;;QACqD,4HAAA,CAAA,cAAW;;;KADhE;uCAyFS", "debugId": null}}, {"offset": {"line": 2663, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAppStore } from '@/stores/appStore';\nimport Timeline from '@/components/Timeline';\nimport PhotoStage from '@/components/PhotoStage';\nimport ProgressIndicator from '@/components/ProgressIndicator';\nimport { motion } from 'framer-motion';\n\nexport default function Home() {\n  const { photos, currentPhotoIndex, setCurrentPhoto } = useAppStore();\n  const currentPhoto = photos[currentPhotoIndex];\n\n  const handlePhotoSelect = (index: number) => {\n    setCurrentPhoto(index);\n  };\n\n  const handlePromptSubmit = async (prompt: string) => {\n    // TODO: Implement video generation logic\n    console.log('Prompt submitted:', prompt);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-vintage-off-white relative\">\n      {/* Film grain overlay */}\n      <div className=\"film-grain fixed inset-0 pointer-events-none z-10\" />\n\n      {/* Progress Indicator */}\n      <ProgressIndicator />\n\n      {/* Main content container */}\n      <div className=\"relative z-20 min-h-screen flex flex-col\">\n        {/* Header */}\n        <motion.header\n          className=\"text-center py-8 md:py-12 px-4\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <h1 className=\"font-playfair text-3xl sm:text-4xl md:text-6xl text-vintage-brown mb-4\">\n            Moments Forward\n          </h1>\n          <p className=\"font-inter text-base md:text-lg text-vintage-brown/80 max-w-2xl mx-auto\">\n            A journey through our shared memories, where each photo unlocks the next chapter of our story\n          </p>\n        </motion.header>\n\n        {/* Main content area */}\n        <div className=\"flex-1 flex flex-col justify-center px-4 max-w-6xl mx-auto w-full\">\n          {/* Photo Stage */}\n          <motion.div\n            className=\"mb-12 md:mb-16\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <PhotoStage\n              photo={currentPhoto}\n              isActive={true}\n              onPromptSubmit={handlePromptSubmit}\n            />\n          </motion.div>\n        </div>\n\n        {/* Timeline - Fixed at bottom */}\n        <motion.div\n          className=\"mt-auto py-8 px-4\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n        >\n          <div className=\"max-w-6xl mx-auto\">\n            <Timeline\n              photos={photos}\n              currentIndex={currentPhotoIndex}\n              onPhotoSelect={handlePhotoSelect}\n            />\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,cAAW,AAAD;IACjE,MAAM,eAAe,MAAM,CAAC,kBAAkB;IAE9C,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,OAAO;QAChC,yCAAyC;QACzC,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,0IAAA,CAAA,UAAiB;;;;;0BAGlB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,6LAAC;gCAAG,WAAU;0CAAyE;;;;;;0CAGvF,6LAAC;gCAAE,WAAU;0CAA0E;;;;;;;;;;;;kCAMzF,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,6LAAC,mIAAA,CAAA,UAAU;gCACT,OAAO;gCACP,UAAU;gCACV,gBAAgB;;;;;;;;;;;;;;;;kCAMtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iIAAA,CAAA,UAAQ;gCACP,QAAQ;gCACR,cAAc;gCACd,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;GAzEwB;;QACiC,4HAAA,CAAA,cAAW;;;KAD5C", "debugId": null}}]}