{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/stores/appStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\nimport { AppState, Photo, VideoGeneration } from '@/types';\n\ninterface AppStore extends AppState {\n  // Actions\n  setCurrentPhoto: (index: number) => void;\n  unlockNextPhoto: () => void;\n  addVideoGeneration: (generation: VideoGeneration) => void;\n  updateVideoGeneration: (id: string, updates: Partial<VideoGeneration>) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | undefined) => void;\n  initializePhotos: (photos: Photo[]) => void;\n  resetProgress: () => void;\n  getVideoForPhoto: (photoId: string) => VideoGeneration | undefined;\n  getCompletedVideos: () => VideoGeneration[];\n  getTotalProgress: () => number;\n}\n\n// Sample data - in a real app, this would come from an API\nconst samplePhotos: Photo[] = [\n  {\n    id: '1',\n    src: '/photos/photo1.svg',\n    alt: 'First childhood memory',\n    date: 'Summer 2010',\n    location: 'Grandma\\'s backyard',\n    isUnlocked: true,\n    contextStory: 'Remember this day? We were playing in <PERSON>\\'s garden, and you had just discovered that butterfly. You were so excited, jumping up and down, trying to catch it with your tiny hands.',\n    brotherSignature: '- Your loving brother ❤️'\n  },\n  {\n    id: '2',\n    src: '/photos/photo2.svg',\n    alt: 'Second childhood memory',\n    date: 'Winter 2011',\n    location: 'Living room',\n    isUnlocked: false,\n    contextStory: 'This was Christmas morning. You had just opened your favorite present - that stuffed elephant you carried everywhere for years. The look of pure joy on your face was priceless.',\n    brotherSignature: '- Your loving brother ❤️'\n  },\n  {\n    id: '3',\n    src: '/photos/photo3.svg',\n    alt: 'Third childhood memory',\n    date: 'Spring 2012',\n    location: 'Local park',\n    isUnlocked: false,\n    contextStory: 'Our first bike ride together! You were so determined to keep up with me, even though your legs could barely reach the pedals. You never gave up.',\n    brotherSignature: '- Your loving brother ❤️'\n  },\n  {\n    id: '4',\n    src: '/photos/photo4.svg',\n    alt: 'Fourth childhood memory',\n    date: 'Summer 2013',\n    location: 'Beach vacation',\n    isUnlocked: false,\n    contextStory: 'Building sandcastles at the beach. You insisted on making the tallest tower, and when the wave knocked it down, you just laughed and started building again.',\n    brotherSignature: '- Your loving brother ❤️'\n  },\n  {\n    id: '5',\n    src: '/photos/photo5.svg',\n    alt: 'Fifth childhood memory',\n    date: 'Fall 2014',\n    location: 'School playground',\n    isUnlocked: false,\n    contextStory: 'Your first day of school. You were nervous but trying to be brave. I promised I\\'d pick you up, and you made me pinky promise. You still remember that, don\\'t you?',\n    brotherSignature: '- Your loving brother ❤️'\n  }\n];\n\nexport const useAppStore = create<AppStore>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      photos: samplePhotos,\n      currentPhotoIndex: 0,\n      videoGenerations: [],\n      isLoading: false,\n      error: undefined,\n\n  // Actions\n  setCurrentPhoto: (index: number) => {\n    const { photos } = get();\n    if (index >= 0 && index < photos.length && photos[index].isUnlocked) {\n      set({ currentPhotoIndex: index });\n    }\n  },\n\n  unlockNextPhoto: () => {\n    set((state) => {\n      const nextIndex = state.currentPhotoIndex + 1;\n      if (nextIndex < state.photos.length) {\n        const updatedPhotos = state.photos.map((photo, index) => \n          index === nextIndex ? { ...photo, isUnlocked: true } : photo\n        );\n        return {\n          photos: updatedPhotos,\n          currentPhotoIndex: nextIndex\n        };\n      }\n      return state;\n    });\n  },\n\n  addVideoGeneration: (generation: VideoGeneration) => {\n    set((state) => ({\n      videoGenerations: [...state.videoGenerations, generation]\n    }));\n  },\n\n  updateVideoGeneration: (id: string, updates: Partial<VideoGeneration>) => {\n    set((state) => ({\n      videoGenerations: state.videoGenerations.map(gen =>\n        gen.id === id ? { ...gen, ...updates } : gen\n      )\n    }));\n  },\n\n  setLoading: (loading: boolean) => {\n    set({ isLoading: loading });\n  },\n\n  setError: (error: string | undefined) => {\n    set({ error });\n  },\n\n  initializePhotos: (photos: Photo[]) => {\n    set({ photos, currentPhotoIndex: 0 });\n  },\n\n  resetProgress: () => {\n    set((state) => ({\n      photos: state.photos.map((photo, index) => ({\n        ...photo,\n        isUnlocked: index === 0 // Only first photo unlocked\n      })),\n      currentPhotoIndex: 0,\n      videoGenerations: [],\n      error: undefined\n    }));\n  },\n\n  getVideoForPhoto: (photoId: string) => {\n    const { videoGenerations } = get();\n    return videoGenerations.find(gen => gen.photoId === photoId && gen.status === 'completed');\n  },\n\n  getCompletedVideos: () => {\n    const { videoGenerations } = get();\n    return videoGenerations.filter(gen => gen.status === 'completed');\n  },\n\n  getTotalProgress: () => {\n    const { photos } = get();\n    const unlockedCount = photos.filter(photo => photo.isUnlocked).length;\n    return Math.round((unlockedCount / photos.length) * 100);\n  }\n}),\n{\n  name: 'moments-forward-storage',\n  storage: createJSONStorage(() => localStorage),\n  partialize: (state) => ({\n    photos: state.photos,\n    currentPhotoIndex: state.currentPhotoIndex,\n    videoGenerations: state.videoGenerations,\n  }),\n}\n));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAkBA,2DAA2D;AAC3D,MAAM,eAAwB;IAC5B;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,YAAY;QACZ,cAAc;QACd,kBAAkB;IACpB;CACD;AAEM,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC9B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,QAAQ;QACR,mBAAmB;QACnB,kBAAkB,EAAE;QACpB,WAAW;QACX,OAAO;QAEX,UAAU;QACV,iBAAiB,CAAC;YAChB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,IAAI,SAAS,KAAK,QAAQ,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnE,IAAI;oBAAE,mBAAmB;gBAAM;YACjC;QACF;QAEA,iBAAiB;YACf,IAAI,CAAC;gBACH,MAAM,YAAY,MAAM,iBAAiB,GAAG;gBAC5C,IAAI,YAAY,MAAM,MAAM,CAAC,MAAM,EAAE;oBACnC,MAAM,gBAAgB,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,QAC7C,UAAU,YAAY;4BAAE,GAAG,KAAK;4BAAE,YAAY;wBAAK,IAAI;oBAEzD,OAAO;wBACL,QAAQ;wBACR,mBAAmB;oBACrB;gBACF;gBACA,OAAO;YACT;QACF;QAEA,oBAAoB,CAAC;YACnB,IAAI,CAAC,QAAU,CAAC;oBACd,kBAAkB;2BAAI,MAAM,gBAAgB;wBAAE;qBAAW;gBAC3D,CAAC;QACH;QAEA,uBAAuB,CAAC,IAAY;YAClC,IAAI,CAAC,QAAU,CAAC;oBACd,kBAAkB,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAA,MAC3C,IAAI,EAAE,KAAK,KAAK;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAE7C,CAAC;QACH;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE;gBAAQ,mBAAmB;YAAE;QACrC;QAEA,eAAe;YACb,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;4BAC1C,GAAG,KAAK;4BACR,YAAY,UAAU,EAAE,4BAA4B;wBACtD,CAAC;oBACD,mBAAmB;oBACnB,kBAAkB,EAAE;oBACpB,OAAO;gBACT,CAAC;QACH;QAEA,kBAAkB,CAAC;YACjB,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,OAAO,iBAAiB,IAAI,CAAC,CAAA,MAAO,IAAI,OAAO,KAAK,WAAW,IAAI,MAAM,KAAK;QAChF;QAEA,oBAAoB;YAClB,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,OAAO,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;QACvD;QAEA,kBAAkB;YAChB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,EAAE,MAAM;YACrE,OAAO,KAAK,KAAK,CAAC,AAAC,gBAAgB,OAAO,MAAM,GAAI;QACtD;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,QAAQ,MAAM,MAAM;YACpB,mBAAmB,MAAM,iBAAiB;YAC1C,kBAAkB,MAAM,gBAAgB;QAC1C,CAAC;AACH", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\n\nexport function cn(...inputs: ClassValue[]) {\n  return clsx(inputs);\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(date);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function validatePrompt(prompt: string, minLength: number = 20): {\n  isValid: boolean;\n  error?: string;\n} {\n  const trimmed = prompt.trim();\n  \n  if (trimmed.length === 0) {\n    return { isValid: false, error: 'Please enter a prompt' };\n  }\n  \n  if (trimmed.length < minLength) {\n    return { \n      isValid: false, \n      error: `Please write at least ${minLength} characters` \n    };\n  }\n  \n  return { isValid: true };\n}\n\nexport function createVideoGenerationRequest(photoId: string, prompt: string) {\n  return {\n    id: generateId(),\n    photoId,\n    prompt,\n    status: 'pending' as const,\n    createdAt: new Date(),\n  };\n}\n\n// Animation variants for Framer Motion\nexport const fadeInUp = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0 },\n  exit: { opacity: 0, y: -20 },\n};\n\nexport const fadeIn = {\n  initial: { opacity: 0 },\n  animate: { opacity: 1 },\n  exit: { opacity: 0 },\n};\n\nexport const scaleIn = {\n  initial: { opacity: 0, scale: 0.9 },\n  animate: { opacity: 1, scale: 1 },\n  exit: { opacity: 0, scale: 0.9 },\n};\n\nexport const slideInFromRight = {\n  initial: { opacity: 0, x: 20 },\n  animate: { opacity: 1, x: 0 },\n  exit: { opacity: 0, x: -20 },\n};\n\n// Stagger animation for lists\nexport const staggerContainer = {\n  animate: {\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n};\n\nexport const staggerItem = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0 },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACd;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,eAAe,MAAc,EAAE,YAAoB,EAAE;IAInE,MAAM,UAAU,OAAO,IAAI;IAE3B,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAwB;IAC1D;IAEA,IAAI,QAAQ,MAAM,GAAG,WAAW;QAC9B,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,UAAU,WAAW,CAAC;QACxD;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEO,SAAS,6BAA6B,OAAe,EAAE,MAAc;IAC1E,OAAO;QACL,IAAI;QACJ;QACA;QACA,QAAQ;QACR,WAAW,IAAI;IACjB;AACF;AAGO,MAAM,WAAW;IACtB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;AAC7B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAE,SAAS;IAAE;IACtB,SAAS;QAAE,SAAS;IAAE;IACtB,MAAM;QAAE,SAAS;IAAE;AACrB;AAEO,MAAM,UAAU;IACrB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,SAAS;QAAE,SAAS;QAAG,OAAO;IAAE;IAChC,MAAM;QAAE,SAAS;QAAG,OAAO;IAAI;AACjC;AAEO,MAAM,mBAAmB;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;AAC7B;AAGO,MAAM,mBAAmB;IAC9B,SAAS;QACP,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;AAC9B", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/Timeline.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Lock, CheckCircle } from 'lucide-react';\nimport Image from 'next/image';\nimport { TimelineProps } from '@/types';\nimport { staggerContainer, staggerItem } from '@/lib/utils';\n\nconst Timeline: React.FC<TimelineProps> = ({ photos, currentIndex, onPhotoSelect }) => {\n  return (\n    <div className=\"w-full max-w-6xl mx-auto\">\n      {/* Desktop Timeline - Horizontal */}\n      <div className=\"hidden md:block\">\n        <div className=\"relative\">\n          {/* Timeline line */}\n          <div className=\"absolute top-1/2 left-0 right-0 h-0.5 bg-vintage-gold/30 transform -translate-y-1/2\" />\n\n          <motion.div\n            className=\"flex items-center justify-between px-8 relative z-10\"\n            variants={staggerContainer}\n            initial=\"initial\"\n            animate=\"animate\"\n          >\n            {photos.map((photo, index) => (\n              <motion.div\n                key={photo.id}\n                className=\"flex flex-col items-center\"\n                variants={staggerItem}\n              >\n                {/* Photo thumbnail */}\n                <motion.div\n                  className={`relative cursor-pointer transition-all duration-300 ${\n                    index === currentIndex\n                      ? 'w-32 h-32 golden-glow'\n                      : 'w-24 h-24 hover:scale-105'\n                  }`}\n                  onClick={() => photo.isUnlocked && onPhotoSelect(index)}\n                  whileHover={photo.isUnlocked ? { scale: 1.05 } : {}}\n                  whileTap={photo.isUnlocked ? { scale: 0.95 } : {}}\n                >\n                  <div className={`\n                    relative w-full h-full rounded-lg overflow-hidden vintage-frame\n                    ${!photo.isUnlocked ? 'opacity-50' : ''}\n                    ${index === currentIndex ? 'ring-4 ring-vintage-gold' : ''}\n                  `}>\n                    <Image\n                      src={photo.src}\n                      alt={photo.alt}\n                      fill\n                      className={`object-cover ${!photo.isUnlocked ? 'blur-sm' : ''}`}\n                      sizes=\"(max-width: 768px) 96px, 128px\"\n                    />\n\n                    {/* Lock/Unlock overlay */}\n                    {!photo.isUnlocked ? (\n                      <div className=\"absolute inset-0 flex items-center justify-center bg-black/20\">\n                        <Lock className=\"w-6 h-6 text-white drop-shadow-lg\" />\n                      </div>\n                    ) : index < currentIndex && (\n                      <motion.div\n                        className=\"absolute top-2 right-2\"\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        <CheckCircle className=\"w-5 h-5 text-vintage-gold drop-shadow-lg\" />\n                      </motion.div>\n                    )}\n\n                    {/* Progress indicator */}\n                    <div className=\"absolute bottom-2 left-2 right-2\">\n                      <div className=\"w-full h-1 bg-white/30 rounded-full overflow-hidden\">\n                        <motion.div\n                          className=\"h-full bg-vintage-gold\"\n                          initial={{ width: 0 }}\n                          animate={{\n                            width: photo.isUnlocked ? '100%' : '0%'\n                          }}\n                          transition={{ duration: 0.5, delay: 0.2 }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </motion.div>\n\n                {/* Photo metadata */}\n                <div className=\"mt-4 text-center max-w-[120px]\">\n                  <p className=\"font-kalam text-sm text-vintage-brown font-medium\">\n                    {photo.date}\n                  </p>\n                  {photo.location && (\n                    <p className=\"font-inter text-xs text-vintage-brown/60 truncate\">\n                      {photo.location}\n                    </p>\n                  )}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Mobile Timeline - Horizontal Scroll */}\n      <div className=\"md:hidden\">\n        <div className=\"relative\">\n          {/* Timeline line */}\n          <div className=\"absolute top-1/2 left-4 right-4 h-0.5 bg-vintage-gold/30 transform -translate-y-1/2\" />\n\n          <div className=\"flex items-center space-x-6 overflow-x-auto px-4 py-4 relative z-10\">\n            {photos.map((photo, index) => (\n              <motion.div\n                key={photo.id}\n                className=\"flex flex-col items-center flex-shrink-0\"\n                variants={staggerItem}\n              >\n                {/* Photo thumbnail */}\n                <motion.div\n                  className={`relative cursor-pointer transition-all duration-300 ${\n                    index === currentIndex\n                      ? 'w-24 h-24 golden-glow'\n                      : 'w-20 h-20'\n                  }`}\n                  onClick={() => photo.isUnlocked && onPhotoSelect(index)}\n                  whileTap={photo.isUnlocked ? { scale: 0.95 } : {}}\n                >\n                  <div className={`\n                    relative w-full h-full rounded-lg overflow-hidden vintage-frame\n                    ${!photo.isUnlocked ? 'opacity-50' : ''}\n                    ${index === currentIndex ? 'ring-2 ring-vintage-gold' : ''}\n                  `}>\n                    <Image\n                      src={photo.src}\n                      alt={photo.alt}\n                      fill\n                      className={`object-cover ${!photo.isUnlocked ? 'blur-sm' : ''}`}\n                      sizes=\"96px\"\n                    />\n\n                    {!photo.isUnlocked ? (\n                      <div className=\"absolute inset-0 flex items-center justify-center bg-black/20\">\n                        <Lock className=\"w-4 h-4 text-white drop-shadow-lg\" />\n                      </div>\n                    ) : index < currentIndex && (\n                      <motion.div\n                        className=\"absolute top-1 right-1\"\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        <CheckCircle className=\"w-4 h-4 text-vintage-gold drop-shadow-lg\" />\n                      </motion.div>\n                    )}\n                  </div>\n                </motion.div>\n\n                {/* Photo info */}\n                <div className=\"mt-2 text-center max-w-[80px]\">\n                  <p className=\"font-kalam text-xs text-vintage-brown font-medium\">\n                    {photo.date}\n                  </p>\n                  {photo.location && (\n                    <p className=\"font-inter text-xs text-vintage-brown/60 truncate\">\n                      {photo.location}\n                    </p>\n                  )}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Timeline;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,WAAoC,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE;IAChF,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU,mHAAA,CAAA,mBAAgB;4BAC1B,SAAQ;4BACR,SAAQ;sCAEP,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,UAAU,mHAAA,CAAA,cAAW;;sDAGrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAW,CAAC,oDAAoD,EAC9D,UAAU,eACN,0BACA,6BACJ;4CACF,SAAS,IAAM,MAAM,UAAU,IAAI,cAAc;4CACjD,YAAY,MAAM,UAAU,GAAG;gDAAE,OAAO;4CAAK,IAAI,CAAC;4CAClD,UAAU,MAAM,UAAU,GAAG;gDAAE,OAAO;4CAAK,IAAI,CAAC;sDAEhD,cAAA,8OAAC;gDAAI,WAAW,CAAC;;oBAEf,EAAE,CAAC,MAAM,UAAU,GAAG,eAAe,GAAG;oBACxC,EAAE,UAAU,eAAe,6BAA6B,GAAG;kBAC7D,CAAC;;kEACC,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,MAAM,GAAG;wDACd,KAAK,MAAM,GAAG;wDACd,IAAI;wDACJ,WAAW,CAAC,aAAa,EAAE,CAAC,MAAM,UAAU,GAAG,YAAY,IAAI;wDAC/D,OAAM;;;;;;oDAIP,CAAC,MAAM,UAAU,iBAChB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;mGAEhB,QAAQ,8BACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,YAAY;4DAAE,UAAU;wDAAI;kEAE5B,cAAA,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAK3B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS;oEACP,OAAO,MAAM,UAAU,GAAG,SAAS;gEACrC;gEACA,YAAY;oEAAE,UAAU;oEAAK,OAAO;gEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQlD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,MAAM,IAAI;;;;;;gDAEZ,MAAM,QAAQ,kBACb,8OAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;;;;;;;;mCAnEhB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;0BA8EvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,UAAU,mHAAA,CAAA,cAAW;;sDAGrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAW,CAAC,oDAAoD,EAC9D,UAAU,eACN,0BACA,aACJ;4CACF,SAAS,IAAM,MAAM,UAAU,IAAI,cAAc;4CACjD,UAAU,MAAM,UAAU,GAAG;gDAAE,OAAO;4CAAK,IAAI,CAAC;sDAEhD,cAAA,8OAAC;gDAAI,WAAW,CAAC;;oBAEf,EAAE,CAAC,MAAM,UAAU,GAAG,eAAe,GAAG;oBACxC,EAAE,UAAU,eAAe,6BAA6B,GAAG;kBAC7D,CAAC;;kEACC,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAK,MAAM,GAAG;wDACd,KAAK,MAAM,GAAG;wDACd,IAAI;wDACJ,WAAW,CAAC,aAAa,EAAE,CAAC,MAAM,UAAU,GAAG,YAAY,IAAI;wDAC/D,OAAM;;;;;;oDAGP,CAAC,MAAM,UAAU,iBAChB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;mGAEhB,QAAQ,8BACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,YAAY;4DAAE,UAAU;wDAAI;kEAE5B,cAAA,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAO/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,MAAM,IAAI;;;;;;gDAEZ,MAAM,QAAQ,kBACb,8OAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;;;;;;;;mCAnDhB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8D7B;uCAEe", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/components/ProgressIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useAppStore } from '@/stores/appStore';\n\nconst ProgressIndicator: React.FC = () => {\n  const { photos, getTotalProgress, getCompletedVideos } = useAppStore();\n  const progress = getTotalProgress();\n  const completedVideos = getCompletedVideos();\n  const unlockedPhotos = photos.filter(photo => photo.isUnlocked).length;\n\n  return (\n    <div className=\"fixed top-4 right-4 z-30\">\n      <motion.div\n        className=\"bg-vintage-cream/90 backdrop-blur-sm rounded-2xl p-3 md:p-4 border border-vintage-gold/20 shadow-lg\"\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <div className=\"flex items-center space-x-3 md:space-x-4\">\n          {/* Circular progress */}\n          <div className=\"relative w-10 h-10 md:w-12 md:h-12\">\n            <svg className=\"w-10 h-10 md:w-12 md:h-12 transform -rotate-90\" viewBox=\"0 0 36 36\">\n              {/* Background circle */}\n              <path\n                className=\"text-vintage-brown/20\"\n                stroke=\"currentColor\"\n                strokeWidth=\"3\"\n                fill=\"transparent\"\n                d=\"M18 2.0845\n                  a 15.9155 15.9155 0 0 1 0 31.831\n                  a 15.9155 15.9155 0 0 1 0 -31.831\"\n              />\n              {/* Progress circle */}\n              <motion.path\n                className=\"text-vintage-gold\"\n                stroke=\"currentColor\"\n                strokeWidth=\"3\"\n                fill=\"transparent\"\n                strokeLinecap=\"round\"\n                d=\"M18 2.0845\n                  a 15.9155 15.9155 0 0 1 0 31.831\n                  a 15.9155 15.9155 0 0 1 0 -31.831\"\n                initial={{ strokeDasharray: \"0 100\" }}\n                animate={{ strokeDasharray: `${progress} 100` }}\n                transition={{ duration: 1, ease: \"easeInOut\" }}\n              />\n            </svg>\n            \n            {/* Progress text */}\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <span className=\"text-xs md:text-sm font-poppins font-semibold text-vintage-brown\">\n                {progress}%\n              </span>\n            </div>\n          </div>\n\n          {/* Progress details */}\n          <div className=\"text-xs md:text-sm hidden sm:block\">\n            <div className=\"font-poppins font-medium text-vintage-brown\">\n              {unlockedPhotos} of {photos.length}\n            </div>\n            <div className=\"font-inter text-vintage-brown/60\">\n              memories unlocked\n            </div>\n            {completedVideos.length > 0 && (\n              <div className=\"font-inter text-vintage-gold text-xs mt-1\">\n                {completedVideos.length} videos created\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Mini timeline */}\n        <div className=\"mt-3 flex space-x-1\">\n          {photos.map((photo, index) => (\n            <motion.div\n              key={photo.id}\n              className={`w-2 h-2 rounded-full ${\n                photo.isUnlocked \n                  ? 'bg-vintage-gold' \n                  : 'bg-vintage-brown/20'\n              }`}\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 0.3, delay: index * 0.1 }}\n            />\n          ))}\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ProgressIndicator;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,oBAA8B;IAClC,MAAM,EAAE,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IACnE,MAAM,WAAW;IACjB,MAAM,kBAAkB;IACxB,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,UAAU,EAAE,MAAM;IAEtE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;oCAAiD,SAAQ;;sDAEtE,8OAAC;4CACC,WAAU;4CACV,QAAO;4CACP,aAAY;4CACZ,MAAK;4CACL,GAAE;;;;;;sDAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAU;4CACV,QAAO;4CACP,aAAY;4CACZ,MAAK;4CACL,eAAc;4CACd,GAAE;4CAGF,SAAS;gDAAE,iBAAiB;4CAAQ;4CACpC,SAAS;gDAAE,iBAAiB,GAAG,SAAS,IAAI,CAAC;4CAAC;4CAC9C,YAAY;gDAAE,UAAU;gDAAG,MAAM;4CAAY;;;;;;;;;;;;8CAKjD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;;4CACb;4CAAS;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ;wCAAe;wCAAK,OAAO,MAAM;;;;;;;8CAEpC,8OAAC;oCAAI,WAAU;8CAAmC;;;;;;gCAGjD,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;8BAOhC,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAW,CAAC,qBAAqB,EAC/B,MAAM,UAAU,GACZ,oBACA,uBACJ;4BACF,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;2BAR3C,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;AAe3B;uCAEe", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAppStore } from '@/stores/appStore';\nimport Timeline from '@/components/Timeline';\nimport PhotoStage from '@/components/PhotoStage';\nimport ProgressIndicator from '@/components/ProgressIndicator';\nimport { motion } from 'framer-motion';\n\nexport default function Home() {\n  const { photos, currentPhotoIndex, setCurrentPhoto } = useAppStore();\n  const currentPhoto = photos[currentPhotoIndex];\n\n  const handlePhotoSelect = (index: number) => {\n    setCurrentPhoto(index);\n  };\n\n  const handlePromptSubmit = async (prompt: string) => {\n    // TODO: Implement video generation logic\n    console.log('Prompt submitted:', prompt);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-vintage-off-white relative\">\n      {/* Film grain overlay */}\n      <div className=\"film-grain fixed inset-0 pointer-events-none z-10\" />\n\n      {/* Progress Indicator */}\n      <ProgressIndicator />\n\n      {/* Main content container */}\n      <div className=\"relative z-20 min-h-screen flex flex-col\">\n        {/* Header */}\n        <motion.header\n          className=\"text-center py-8 md:py-12 px-4\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <h1 className=\"font-playfair text-3xl sm:text-4xl md:text-6xl text-vintage-brown mb-4\">\n            Moments Forward\n          </h1>\n          <p className=\"font-inter text-base md:text-lg text-vintage-brown/80 max-w-2xl mx-auto\">\n            A journey through our shared memories, where each photo unlocks the next chapter of our story\n          </p>\n        </motion.header>\n\n        {/* Main content area */}\n        <div className=\"flex-1 flex flex-col justify-center px-4 max-w-6xl mx-auto w-full\">\n          {/* Photo Stage */}\n          <motion.div\n            className=\"mb-12 md:mb-16\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <PhotoStage\n              photo={currentPhoto}\n              isActive={true}\n              onPromptSubmit={handlePromptSubmit}\n            />\n          </motion.div>\n        </div>\n\n        {/* Timeline - Fixed at bottom */}\n        <motion.div\n          className=\"mt-auto py-8 px-4\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n        >\n          <div className=\"max-w-6xl mx-auto\">\n            <Timeline\n              photos={photos}\n              currentIndex={currentPhotoIndex}\n              onPhotoSelect={handlePhotoSelect}\n            />\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IACjE,MAAM,eAAe,MAAM,CAAC,kBAAkB;IAE9C,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,OAAO;QAChC,yCAAyC;QACzC,QAAQ,GAAG,CAAC,qBAAqB;IACnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC,uIAAA,CAAA,UAAiB;;;;;0BAGlB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBAC<PERSON>,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC;gCAAG,WAAU;0CAAyE;;;;;;0CAGvF,8OAAC;gCAAE,WAAU;0CAA0E;;;;;;;;;;;;kCAMzF,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,gIAAA,CAAA,UAAU;gCACT,OAAO;gCACP,UAAU;gCACV,gBAAgB;;;;;;;;;;;;;;;;kCAMtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8HAAA,CAAA,UAAQ;gCACP,QAAQ;gCACR,cAAc;gCACd,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}]}