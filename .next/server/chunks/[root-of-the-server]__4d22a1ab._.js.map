{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Personal/Project/rakhi_ai/moments_forward/src/app/api/generate-video/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\ninterface VideoGenerationRequest {\n  imageUrl: string;\n  prompt: string;\n}\n\ninterface RunwayMLResponse {\n  id: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  videoUrl?: string;\n  error?: string;\n}\n\n// Mock RunwayML API integration\n// In a real implementation, you would use the actual RunwayML API\nasync function generateVideoWithRunwayML(imageUrl: string, prompt: string): Promise<RunwayMLResponse> {\n  // TODO: Replace with actual RunwayML API call\n  // Example implementation:\n  /*\n  const response = await fetch('https://api.runwayml.com/v1/generate', {\n    method: 'POST',\n    headers: {\n      'Authorization': `Bearer ${process.env.RUNWAY_API_KEY}`,\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      model: 'gen-3-alpha-turbo',\n      image: imageUrl,\n      text_prompt: prompt,\n      duration: 10, // 10 seconds\n      ratio: '16:9',\n    }),\n  });\n\n  if (!response.ok) {\n    throw new Error(`RunwayML API error: ${response.statusText}`);\n  }\n\n  const data = await response.json();\n  return data;\n  */\n\n  // Mock response for development\n  await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API delay\n\n  return {\n    id: `mock_${Date.now()}`,\n    status: 'completed',\n    videoUrl: `/videos/mock_generated_video.mp4`,\n  };\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body: VideoGenerationRequest = await request.json();\n    const { imageUrl, prompt } = body;\n\n    // Validate input\n    if (!imageUrl || !prompt) {\n      return NextResponse.json(\n        { error: 'Missing required fields: imageUrl and prompt' },\n        { status: 400 }\n      );\n    }\n\n    if (prompt.length < 10) {\n      return NextResponse.json(\n        { error: 'Prompt must be at least 10 characters long' },\n        { status: 400 }\n      );\n    }\n\n    // Generate video using RunwayML\n    const result = await generateVideoWithRunwayML(imageUrl, prompt);\n\n    if (result.status === 'failed') {\n      return NextResponse.json(\n        { error: result.error || 'Video generation failed' },\n        { status: 500 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      generationId: result.id,\n      status: result.status,\n      videoUrl: result.videoUrl,\n    });\n\n  } catch (error) {\n    console.error('Video generation error:', error);\n    \n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// GET endpoint to check generation status\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const generationId = searchParams.get('id');\n\n  if (!generationId) {\n    return NextResponse.json(\n      { error: 'Missing generation ID' },\n      { status: 400 }\n    );\n  }\n\n  // TODO: Implement actual status checking with RunwayML API\n  // For now, return mock completed status\n  return NextResponse.json({\n    id: generationId,\n    status: 'completed',\n    videoUrl: `/videos/mock_generated_video.mp4`,\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAcA,gCAAgC;AAChC,kEAAkE;AAClE,eAAe,0BAA0B,QAAgB,EAAE,MAAc;IACvE,8CAA8C;IAC9C,0BAA0B;IAC1B;;;;;;;;;;;;;;;;;;;;;;EAsBA,GAEA,gCAAgC;IAChC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,qBAAqB;IAE9E,OAAO;QACL,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;QACxB,QAAQ;QACR,UAAU,CAAC,gCAAgC,CAAC;IAC9C;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAA+B,MAAM,QAAQ,IAAI;QACvD,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;QAE7B,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,QAAQ;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,OAAO,MAAM,GAAG,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6C,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,SAAS,MAAM,0BAA0B,UAAU;QAEzD,IAAI,OAAO,MAAM,KAAK,UAAU;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,OAAO,KAAK,IAAI;YAA0B,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,cAAc,OAAO,EAAE;YACvB,QAAQ,OAAO,MAAM;YACrB,UAAU,OAAO,QAAQ;QAC3B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,eAAe,aAAa,GAAG,CAAC;IAEtC,IAAI,CAAC,cAAc;QACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;IAEA,2DAA2D;IAC3D,wCAAwC;IACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,IAAI;QACJ,QAAQ;QACR,UAAU,CAAC,gCAAgC,CAAC;IAC9C;AACF", "debugId": null}}]}